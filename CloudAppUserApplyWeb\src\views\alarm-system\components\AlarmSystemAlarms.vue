<template>
  <div class="alarm-box-wrapper alarm-system-alarms">
    <div class="alarms-header">
      <div class="alarms-title">Alarms</div>
      <theme-image class="expand-icon" imageName="arrow_right.png" alt="expand" @click="handleExpandClick" />
    </div>
    <div class="alarms-content">
      <div v-if="!alarms?.length" class="no-data-box">
        <span class="no-data-text">{{ $t('noData') }}</span>
      </div>
      <template v-else>
        <div
          v-for="alarm in alarms"
          :key="alarm.id"
          class="alarm-bottom-box alarm-item"
          @click="handleAlarmClick(alarm)"
        >
          <div class="alarm-icon">
            <theme-image class="alarm-indicator" imageName="alarm-system/alarm.png" alt="alarm" />
          </div>
          <div class="alarm-details">
            <div class="alarm-type">{{ alarm.type }}</div>
            <div class="alarm-sub-text alarm-time">{{ alarm.time }}</div>
          </div>
        </div>
      </template>
    </div>
  </div>
</template>

<script>
import { mapState, mapActions, mapGetters } from 'vuex'

export default {
  name: 'AlarmSystemAlarms',
  data() {
    return {}
  },
  computed: {
    ...mapState('alarmSystem', ['siteLoginInfo']),
    ...mapGetters('alarmSystem', ['siteId', 'sessionId', 'systemType', 'latestAlarm']),
    // 从store中获取alarm数据
    alarms() {
      return this.latestAlarm ? [this.latestAlarm] : []
    },
    // 检查是否有足够的数据来执行API调用
    canFetchAlarms() {
      return this.siteId && this.sessionId
    }
  },
  mounted() {
    this.fetchAlarmData()
  },
  methods: {
    ...mapActions('alarmSystem', ['fetchAlarms']),
    // 获取报警数据
    async fetchAlarmData() {
      if (!this.canFetchAlarms) {
        console.warn('Missing required data for fetching alarms')
        return
      }
      try {
        await this.fetchAlarms({
          siteId: this.siteId,
          sessionToken: this.sessionId,
          systemType: this.systemType,
          count: 100
        })
      } catch (error) {
        console.error('Fetch alarms failed:', error)
        this.handleAlarmError(error)
      }
    },
    // 统一的错误处理
    handleAlarmError(error) {
      const errorMessage = error.code ? this.$t(error.code) : error.message
      this.$toast.fail(errorMessage)
    },
    handleAlarmClick(alarm) {
      this.$emit('alarm-click', alarm)
    },
    // 处理右侧展开图标点击
    handleExpandClick() {
      // 跳转到Alarms详情页面
      this.$router.push({
        path: '/alarmSystem/alarms',
        query: {
          siteId: this.siteLoginInfo?.siteId,
          systemType: this.$route.query.systemType
        }
      })
    }
  }
}
</script>

<style lang="scss" scoped>
.alarm-system-alarms {
  padding: 0px 16px;
  box-sizing: border-box;
  .alarms-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    height: 46px;
    .alarms-title {
      font-size: 16px;
      font-weight: 500;
    }
    .expand-icon {
      width: 24px;
      height: 24px;
    }
  }
  .alarms-content {
    min-height: 65px;
    .no-data-box {
      display: flex;
      align-items: center;
      justify-content: center;
      padding: 20px 0;
      .no-data-text {
        font-size: 12px;
      }
    }
    .alarm-item {
      display: flex;
      align-items: center;
      cursor: pointer;
      &:last-child {
        border-bottom: none;
      }
      .alarm-icon {
        margin-right: 12px;
        .alarm-indicator {
          width: 32px;
          height: 32px;
        }
      }
      .alarm-details {
        flex: 1;
        flex-direction: column;
        justify-content: center;
        .alarm-type {
          height: 18px;
          font-weight: 400;
          font-size: 12px;
          line-height: 18px;
          margin-bottom: 5px;
        }
        .alarm-time {
          height: 18px;
          font-weight: 400;
          font-size: 12px;
          line-height: 18px;
        }
      }
    }
  }
}
</style>
