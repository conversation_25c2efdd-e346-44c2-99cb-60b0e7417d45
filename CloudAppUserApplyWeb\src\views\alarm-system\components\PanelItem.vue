<template>
  <div class="panel-item">
    <!-- 上部分：左右布局 -->
    <div class="panel-header">
      <div class="panel-header-content">
        <div class="panel-info">
          <div class="panel-name">{{ panel.name }}</div>
          <div class="panel-id">{{ panel.serialNumber }}</div>
        </div>
        <div class="panel-right-action">
          <div class="action-icon login-icon" @click="handleLogin">
            <theme-image class="icon-img" alt="login" imageName="entry.png" />
          </div>
        </div>
      </div>
    </div>

    <!-- 下部分：两个图标 -->
    <div class="panel-footer">
      <div class="footer-action edit-action" @click="handleEdit">
        <theme-image class="icon-img" alt="edit" imageName="edit.png" />
      </div>
      <div class="footer-action delete-action" @click="handleDelete">
        <theme-image class="icon-img" alt="delete" imageName="delete.png" />
      </div>
    </div>
  </div>
</template>

<script>
export default {
  name: 'PanelItem',
  props: {
    panel: {
      type: Object,
      required: true,
      validator(value) {
        return value && typeof value.id !== 'undefined' && value.name && value.serialNumber
      }
    }
  },
  methods: {
    handleLogin() {
      this.$emit('login', this.panel)
    },
    handleEdit() {
      this.$emit('edit', this.panel)
    },
    handleDelete() {
      this.$emit('delete', this.panel)
    }
  }
}
</script>

<style lang="scss" scoped>
.panel-item {
  background-color: #333;
  color: #fff;
  border-radius: 8px;
  overflow: hidden;

  .panel-header {
    padding: 16px;

    .panel-header-content {
      display: flex;
      justify-content: space-between;
      align-items: center;

      .panel-info {
        flex: 1;

        .panel-name {
          font-size: 16px;
          font-weight: 600;
          margin-bottom: 4px;
        }

        .panel-id {
          font-size: 14px;
          color: #999;
        }
      }

      .panel-right-action {
        .action-icon {
          width: 40px;
          height: 40px;
          display: flex;
          align-items: center;
          justify-content: center;
          cursor: pointer;
          border-radius: 6px;
          transition: background-color 0.2s ease;

          .icon-img {
            width: 24px;
            height: 24px;
          }
        }
      }
    }
  }

  .panel-footer {
    display: flex;
    border-top: 1px solid #444;

    .footer-action {
      flex: 1;
      height: 48px;
      display: flex;
      align-items: center;
      justify-content: center;
      cursor: pointer;
      transition: background-color 0.2s ease;

      .icon-img {
        width: 20px;
        height: 20px;
      }
    }
  }
}
</style>
