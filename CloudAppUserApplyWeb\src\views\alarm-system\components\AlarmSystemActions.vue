<template>
  <div class="alarm-box-wrapper alarm-system-actions">
    <div class="actions-header">
      <div class="actions-title">{{ $t('action') }}</div>
      <theme-image
        v-if="shouldShowExpandButton"
        class="expand-icon"
        imageName="arrow_right.png"
        alt="expand"
        @click="handleExpandClick"
      />
    </div>
    <div class="actions-buttons">
      <div
        v-for="action in actions"
        :key="action.type"
        :class="['action-button', action.type, { disabled: !canPerformAction }]"
        @click="handleAction(action)"
      >
        <theme-image class="action-icon" :imageName="`alarm-system/${action.icon}`" :alt="action.type" />
        <span class="action-text">{{ $t(action.type) }}</span>
      </div>
    </div>
  </div>
</template>

<script>
import { mapState, mapMutations, mapActions, mapGetters } from 'vuex'
import { armPanel, setPimaPartitions } from '@/api/alarmSystem'

const ACTION_CONFIGS = [
  { type: 'home', icon: 'home.png' },
  { type: 'away', icon: 'away.png' },
  { type: 'disarm', icon: 'disarm.png' }
]

// 系统状态码映射常量
const SYSTEM_STATUS_MAPPING = {
  PIMA: {
    home: 3, // Home1
    away: 2, // FullArm
    disarm: 1 // Disarm
  },
  RISCO: {
    home: 2, // PartialArm
    away: 1, // FullArm
    disarm: 0 // Disarmed
  }
}

export default {
  name: 'AlarmSystemActions',
  data() {
    return {
      loading: false,
      actions: ACTION_CONFIGS
    }
  },
  computed: {
    ...mapState('alarmSystem', ['siteLoginInfo', 'panelState']),
    ...mapGetters('alarmSystem', ['systemType', 'isPimaSystem', 'siteId', 'sessionId', 'canFetchPanelState']),
    // 判断是否有足够的数据进行API调用
    canPerformAction() {
      return this.canFetchPanelState && !this.loading
    },
    // 获取当前系统的状态码映射
    systemStatusMapping() {
      return this.isPimaSystem ? SYSTEM_STATUS_MAPPING.PIMA : SYSTEM_STATUS_MAPPING.RISCO
    },
    shouldShowExpandButton() {
      // 拥有多个分区时显示
      return this.panelState.partitionCount > 1
    }
  },
  mounted() {
    // 开发环境下模拟分区数据用于测试
    if (process.env.NODE_ENV === 'development') {
      this.simulatePartitionData()
    }
  },
  methods: {
    ...mapMutations('alarmSystem', ['SET_PANEL_STATE_INFO']),
    ...mapActions('alarmSystem', ['fetchPanelState']),
    // 模拟分区数据
    simulatePartitionData() {
      // 可以通过URL参数控制分区数量进行测试
      // 例如：?systemType=risco&partitions=3
      const urlPartitions = this.$route.query.partitions
      const mockPartitionCount = urlPartitions ? parseInt(urlPartitions) : 2 // 默认模拟2个分区

      // 模拟分区数据，使用不同的状态进行测试
      const mockPartitions = []
      const statusOptions = ['disarmed', 'armed_home', 'armed_away', 'mixed']

      for (let i = 1; i <= mockPartitionCount; i++) {
        mockPartitions.push({
          id: i,
          name: `Partition ${i}`,
          status: statusOptions[(i - 1) % statusOptions.length], // 循环使用不同状态
          devices: [
            { id: `${i}-1`, name: `Door Sensor ${i}-1`, type: 'door' },
            { id: `${i}-2`, name: `Motion Detector ${i}-2`, type: 'motion' }
          ]
        })
      }

      // 构造模拟的面板状态数据
      const mockPanelData = {
        partitions: mockPartitions,
        state: {
          status: [1], // 示例状态码
          isOnline: true,
          media: 1
        }
      }

      // 直接调用mutation更新store中的面板状态
      this.SET_PANEL_STATE_INFO(mockPanelData)
    },
    // 处理操作按钮点击
    async handleAction(action) {
      if (!this.canPerformAction) {
        console.warn('Missing required data for action')
        return
      }
      this.loading = true
      try {
        await this.executeSystemAction(action)
        await this.refreshPanelState()
        this.$toast.success(this.$t('operationSuccess'))
      } catch (error) {
        this.handleActionError()
      } finally {
        this.loading = false
      }
    },
    // 执行系统特定的操作
    async executeSystemAction(action) {
      if (this.isPimaSystem) {
        return this.executePimaAction(action)
      } else {
        return this.executeRiscoAction(action)
      }
    },
    // Pima系统操作处理
    async executePimaAction(action) {
      const statusCode = this.systemStatusMapping[action.type]
      const partitions = this.panelState.partitions || []
      const partitionData =
        partitions.length > 0
          ? partitions.map(partition => ({
              Number: partition.Number || partition.id || 1,
              Status: statusCode
            }))
          : [{ Number: 1, Status: statusCode }]

      await setPimaPartitions({ data: partitionData })
    },
    // Risco系统操作处理
    async executeRiscoAction(action) {
      await armPanel(this.siteId, {
        newSystemStatus: this.systemStatusMapping[action.type],
        sessionToken: this.sessionId
      })
    },
    // 统一的错误处理
    handleActionError(error) {
      const errorMessage = error.code ? this.$t(error.code) : error.message
      this.$toast.fail(errorMessage)
      // 记录详细错误信息用于调试
      console.error('Action Error Details:', error)
    },
    // 刷新面板状态
    async refreshPanelState() {
      try {
        await this.fetchPanelState({
          siteId: this.siteId,
          sessionToken: this.sessionId,
          systemType: this.systemType
        })
      } catch (error) {
        console.error('刷新面板状态失败:', error)
        // 刷新失败不影响主要操作，只记录日志
      }
    },
    // 处理右侧展开图标点击
    handleExpandClick() {
      // 跳转到Actions页面
      this.$router.push({
        path: '/alarmSystem/actions',
        query: {
          systemType: this.systemType,
          siteId: this.siteId
        }
      })
    }
  }
}
</script>

<style lang="scss" scoped>
.alarm-system-actions {
  padding: 10px 16px;
  box-sizing: border-box;
  .actions-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 16px;
    .actions-title {
      font-family: PingFangSC-Semibold, sans-serif;
      font-weight: 600;
      font-size: 14px;
      line-height: 22px;
    }
    .expand-icon {
      width: 24px;
      height: 24px;
      cursor: pointer;
    }
  }
  .actions-buttons {
    display: flex;
    justify-content: space-between;
    gap: 4px;
    .action-button {
      width: 81px;
      height: 35px;
      display: flex;
      justify-content: center;
      align-items: center;
      border-radius: 8px;
      box-sizing: border-box;
      cursor: pointer;

      &.disabled {
        opacity: 0.5;
        cursor: not-allowed;
      }

      .action-icon {
        .theme-image-container {
          width: 20px;
          height: 20px;
        }
      }
      .action-text {
        height: 20px;
        font-family: PingFangSC-Regular, sans-serif;
        font-weight: 400;
        font-size: 14px;
        letter-spacing: 0;
        margin-left: 3px;
      }
      &:active:not(.disabled) {
        opacity: 0.8;
      }
    }
  }
}
</style>
