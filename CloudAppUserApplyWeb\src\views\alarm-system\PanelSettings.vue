<template>
  <div class="panel-settings">
    <!-- 页面内容 -->
    <div class="panel-content">
      <!-- DSC 标题 -->
      <div class="dsc-title">DSC</div>

      <!-- Settings 区域 -->
      <div class="alarm-box-wrapper settings-section">
        <!-- 设置列表 -->
        <div class="settings-list">
          <div
            v-for="(item, index) in settingsItems"
            :key="index"
            class="alarm-bottom-box settings-item"
            @click="handleSettingClick(item)"
          >
            <div class="setting-content">
              <div class="setting-name">{{ item.name }}</div>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- 底部导航组件 -->
    <alarm-bottom-navigation :init-active-index="4" />

    <!-- 安装商访问确认弹窗 -->
    <van-dialog
      v-model="showInstallerAccessDialog"
      title=""
      :show-cancel-button="true"
      cancel-button-text="Cancel"
      confirm-button-text="OK"
      @confirm="handleInstallerAccessConfirm"
      @cancel="handleInstallerAccessCancel"
      class="common-dialog"
    >
      <div class="common-dialog-content">
        <div class="dialog-message">Do you really want to approve remote installer access?</div>
      </div>
    </van-dialog>

    <!-- 退出登录确认弹窗 -->
    <van-dialog
      v-model="showLogoutDialog"
      title=""
      :show-cancel-button="true"
      cancel-button-text="Cancel"
      confirm-button-text="OK"
      @confirm="handleLogoutConfirm"
      @cancel="handleLogoutCancel"
      class="common-dialog"
    >
      <div class="common-dialog-content">
        <div class="dialog-message">Do you really want to logout?</div>
      </div>
    </van-dialog>
  </div>
</template>

<script>
import AlarmBottomNavigation from './components/AlarmBottomNavigation.vue'

export default {
  name: 'PanelSettings',
  components: {
    AlarmBottomNavigation
  },
  data() {
    return {
      showInstallerAccessDialog: false,
      showLogoutDialog: false,
      settingsItems: [
        {
          name: 'Notifications',
          action: 'notifications'
        },
        {
          name: 'Users',
          action: 'users'
        },
        {
          name: 'Panel Date and Time',
          action: 'panelDateTime'
        },
        {
          name: 'Installer Access',
          action: 'installerAccess'
        },
        {
          name: 'Logout',
          action: 'logout'
        }
      ]
    }
  },
  methods: {
    handleSettingClick(item) {
      switch (item.action) {
        case 'notifications':
          // 跳转到通知设置页面
          this.$router.push('/alarmSystem/notifications')
          break
        case 'users':
          // 跳转到用户管理页面
          this.$router.push('/alarmSystem/users')
          break
        case 'panelDateTime':
          // 跳转到面板日期时间页面
          this.$router.push('/alarmSystem/panelDateTime')
          break
        case 'installerAccess':
          // 显示确认弹窗
          this.showInstallerAccessDialog = true
          break
        case 'logout':
          // 显示退出登录确认弹窗
          this.showLogoutDialog = true
          break
        default:
          this.$toast(`点击了 ${item.name}`)
      }
    },

    handleLogoutConfirm() {
      this.showLogoutDialog = false
      // 清除用户信息
      this.$store.commit('alarmSystem/CLEAR_USER_INFO')
      // 跳转到登录页面
      this.$router.push('/alarmSystem/chooseSystem')
      this.$toast('已退出登录')
    },

    handleLogoutCancel() {
      this.showLogoutDialog = false
      // 用户取消操作，不需要额外处理
    },

    handleInstallerAccessConfirm() {
      this.showInstallerAccessDialog = false
      this.$toast('已批准远程安装商访问')
      // 这里可以调用API来批准安装商访问
    },

    handleInstallerAccessCancel() {
      this.showInstallerAccessDialog = false
      // 用户取消操作，不需要额外处理
    }
  }
}
</script>

<style lang="scss" scoped>
.panel-settings {
  height: 100%;
  display: flex;
  flex-direction: column;

  .panel-content {
    flex: 1;
    overflow-y: auto;
    padding-bottom: 80px;
  }
  .dsc-title {
    height: 40px;
    box-sizing: border-box;
    font-size: 18px;
    font-weight: bold;
    padding: 10px 16px 6px 16px;
  }
  .settings-list {
    .settings-item {
      width: 100%;
      height: 46px;
      padding: 12px 16px;
      box-sizing: border-box;
      .setting-content {
        flex: 1;
        .setting-name {
          font-size: 16px;
          font-weight: 400;
          line-height: 22px;
        }
      }
    }
  }
}
.common-dialog-content {
  padding: 20px;
  text-align: center;
  .dialog-message {
    font-weight: 400;
    font-size: 16px;
    line-height: 24px;
  }
}
</style>
