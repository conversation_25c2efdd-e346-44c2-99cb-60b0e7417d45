<template>
  <div class="alarm-system-wrapper">
    <!-- 所有报警系统子页面都会在这里渲染 -->
    <router-view />
  </div>
</template>

<script>
export default {
  name: 'AlarmSystemLayout',
  created() {
    // TODO: 报警系统入口初始化
    console.log('Alarm System Layout initialized')
    // 可以在这里添加全局的报警系统初始化逻辑
    // 比如检查用户权限、设置全局状态等
  }
}
</script>

<style lang="scss" scoped>
.alarm-system-wrapper {
  height: 100%;
  width: 100%;
}
</style>
