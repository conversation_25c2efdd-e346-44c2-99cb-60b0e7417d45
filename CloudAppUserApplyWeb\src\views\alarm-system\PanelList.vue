<template>
  <div class="panel-list">
    <nav-bar :title="$t('alarmSystem')" @clickLeft="goBack" :showAdd="true" @showAdd="addPanel" />
    <tvt-better-scroll class="tvt-better-scroll" @pullingDown="pullingDown" :pullingStatus="pullingStatus">
      <div class="panel-content">
        <template v-if="siteList.length">
          <panel-item
            v-for="panel in siteList"
            :key="panel.id"
            :panel="panel"
            @login="loginPanel"
            @edit="editPanel"
            @delete="deletePanel"
          />
        </template>
        <empty-state v-else />
      </div>
    </tvt-better-scroll>

    <!-- 编辑面板名称弹窗 -->
    <van-dialog
      v-model="showEditDialog"
      :title="$t('enterPanelName')"
      :show-cancel-button="true"
      :cancel-button-text="$t('cancel')"
      :confirm-button-text="$t('ok')"
      :before-close="handleEditBeforeClose"
      @opened="handleEditDialogOpened"
      class="edit-panel-dialog"
    >
      <div class="edit-dialog-content">
        <common-input
          v-model="editPanelName"
          :placeholder="$t('enterPanelName')"
          :errorMessage="editPanelNameError"
          borderType="full"
          ref="editField"
        />
      </div>
    </van-dialog>
  </div>
</template>

<script>
import PanelItem from './components/PanelItem.vue'
import CommonInput from './components/CommonInput.vue'
import EmptyState from './components/EmptyState.vue'
import { mapState, mapActions, mapMutations, mapGetters } from 'vuex'
import { setPairName, unpairWebUser } from '@/api/alarmSystem'

// 路由常量
const ROUTE_ADD_PANEL = '/alarmSystem/addPanel'
const ROUTE_PANEL_LOGIN = '/alarmSystem/panelLogin'

export default {
  name: 'PanelList',
  components: {
    PanelItem,
    CommonInput,
    EmptyState
  },
  data() {
    return {
      showEditDialog: false,
      editPanelName: '',
      editPanelNameError: '',
      hasNameEmptyError: false, // 添加错误状态标识
      currentEditPanel: null,
      pullingStatus: 0,
      // 防止重复操作的状态
      isProcessing: false
    }
  },
  computed: {
    ...mapState('alarmSystem', ['siteList']),
    ...mapGetters('alarmSystem', ['systemType', 'isPimaSystem'])
  },
  watch: {
    editPanelName: {
      handler(newVal) {
        // 当输入内容时，清除空值错误状态
        if (newVal && this.hasNameEmptyError) {
          this.editPanelNameError = ''
          this.hasNameEmptyError = false
        }
      }
    }
  },
  async created() {
    await this.loadPanels()
  },
  methods: {
    ...mapActions('alarmSystem', ['loadSiteList']),
    ...mapMutations('alarmSystem', ['UPDATE_SITE_IN_LIST', 'DELETE_SITE_FROM_LIST']),
    goBack() {
      this.$router.back()
    },
    // 下拉刷新处理
    pullingDown(callback) {
      this.loadPanels().finally(() => {
        callback && callback()
      })
    },
    // 主加载方法 - 使用 store 管理
    async loadPanels() {
      // 调用 store action 加载数据
      try {
        await this.loadSiteList()
      } catch (error) {
        this.handleApiError(error)
      }
    },
    // 统一的API错误处理
    handleApiError(error) {
      const errorMessage = error.code ? this.$t(error.code) : error.message
      this.$toast.fail(errorMessage)
      // 记录详细错误信息用于调试
      console.error('API Error Details:', error)
    },
    // 清理编辑弹窗状态
    clearEditDialogState() {
      this.editPanelName = ''
      this.editPanelNameError = ''
      this.currentEditPanel = null
    },
    loginPanel(panel) {
      // 跳转到面板登录页面，携带必要参数
      this.$router.push({
        path: ROUTE_PANEL_LOGIN,
        query: {
          systemType: this.systemType,
          siteId: panel.id,
          siteName: panel.name,
          serialNumber: panel.serialNumber
        }
      })
    },
    // 编辑面板
    editPanel(panel) {
      this.currentEditPanel = panel
      this.editPanelName = panel.name
      this.editPanelNameError = ''
      this.showEditDialog = true
    },
    // 删除面板
    async deletePanel(panel) {
      if (this.isProcessing) return
      try {
        await this.$dialog.confirm({
          title: this.$t('deleteConfirm'),
          message: this.$t('deleteConfirmPanel', [panel.name]),
          confirmButtonText: this.$t('delete'),
          cancelButtonText: this.$t('cancel'),
          className: 'common-dialog'
        })
        this.isProcessing = true
        this.$loading.show()
        // 根据系统类型调用对应的删除API
        if (this.isPimaSystem) {
          await unpairWebUser({ data: panel.id })
        } else {
          // Risco系统删除 - 实际项目中应该调用真实API
          console.warn('Risco system panel deletion not implemented yet')
        }
        this.DELETE_SITE_FROM_LIST(panel.id)
        this.$toast.success(this.$t('deleteSuccess'))
      } catch (error) {
        if (error !== 'cancel') {
          this.handleApiError(error)
        }
      } finally {
        this.isProcessing = false
        this.$loading.hide()
      }
    },
    // 新增面板
    addPanel() {
      // 跳转到新增面板页面
      this.$router.push({
        path: ROUTE_ADD_PANEL,
        query: {
          systemType: this.systemType
        }
      })
    },
    // 弹窗打开完成后的聚焦处理
    handleEditDialogOpened() {
      // 弹窗完全打开后聚焦输入框
      this.$nextTick(() => {
        if (this.$refs.editField) {
          this.$refs.editField.focus()
        }
      })
    },
    // 处理编辑弹窗关闭前的逻辑
    async handleEditBeforeClose(action, done) {
      if (action === 'confirm') {
        await this.handleConfirmEdit(done)
      } else {
        this.handleCancelEdit(done)
      }
    },
    // 处理确认编辑操作
    async handleConfirmEdit(done) {
      if (!this.validatePanelName()) {
        done(false)
        return
      }
      try {
        await this.performUpdatePanel(this.currentEditPanel, this.editPanelName.trim())
        done(true)
        this.clearEditDialogState()
      } catch (error) {
        // 更新失败，不关闭弹窗
        done(false)
      }
    },
    // 验证面板名称
    validatePanelName() {
      this.editPanelNameError = ''
      this.hasNameEmptyError = false
      if (!this.editPanelName.trim()) {
        this.editPanelNameError = this.$t('panelNameCannotBeEmpty')
        this.hasNameEmptyError = true
        return false
      }
      return true
    },
    // 处理取消编辑操作
    handleCancelEdit(done) {
      done(true)
      this.clearEditDialogState()
    },
    // 面板更新主入口
    async performUpdatePanel(panel, newName) {
      this.$loading.show()
      try {
        // 根据系统类型调用对应的更新API
        if (this.isPimaSystem) {
          await setPairName({ data: newName })
        } else {
          // Risco系统更新 - 实际项目中应该调用真实API
          console.warn('Risco system name update not implemented yet')
        }
        // API调用成功后，更新Store中的状态
        this.UPDATE_SITE_IN_LIST({
          siteId: panel.id,
          updates: { name: newName }
        })
        this.$toast.success(this.$t('panelNameUpdatedSuccessfully'))
      } catch (error) {
        this.handleApiError(error)
        this.$toast.fail(this.$t('updateFailedTryAgain'))
        throw error
      } finally {
        this.$loading.hide()
      }
    }
  }
}
</script>

<style lang="scss" scoped>
.panel-list {
  display: flex;
  flex-direction: column;
  height: 100%;
  .tvt-better-scroll {
    flex: 1;
    height: calc(100% - 64px);
  }
  .panel-content {
    padding: 20px 16px;
    display: flex;
    flex-direction: column;
    gap: 10px;
  }
}
</style>
<style lang="scss">
.edit-panel-dialog,
.common-dialog {
  .van-dialog__header {
    padding: 20px 20px 16px 20px !important;
  }
  .van-dialog__content {
    padding: 0 20px 20px 20px !important;
  }
}
</style>
