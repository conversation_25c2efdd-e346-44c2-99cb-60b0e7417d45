<template>
  <div class="panel-datetime">
    <!-- 顶部导航栏 -->
    <nav-bar title="Panel Date and Time" @clickLeft="goBack" />

    <!-- 页面内容 -->
    <div class="datetime-content">
      <!-- 日历图标 -->
      <div class="calendar-icon-container">
        <div class="calendar-icon-bg">
          <theme-image class="calendar-icon" imageName="alarm-system/calendar.png" alt="calendar" />
        </div>
      </div>

      <!-- 日期时间设置 -->
      <div class="datetime-settings">
        <!-- 日期设置 -->
        <div class="setting-item">
          <div class="setting-label">Date</div>
          <div class="setting-value" @click="handleDateClick">
            {{ formattedDate }}
          </div>
          <div class="setting-divider"></div>
        </div>

        <!-- 时间设置 -->
        <div class="setting-item">
          <div class="setting-label">Time</div>
          <div class="setting-value" @click="handleTimeClick">
            {{ formattedTime }}
          </div>
          <div class="setting-divider"></div>
        </div>
      </div>
    </div>

    <!-- 底部保存按钮 -->
    <div class="save-button-container">
      <van-button class="save-button" @click="handleSave">Save</van-button>
    </div>

    <!-- 日期选择器弹窗 -->
    <van-popup v-model="showDatePicker" position="bottom">
      <van-datetime-picker
        v-model="selectedDate"
        type="date"
        title="选择日期"
        :min-date="minDate"
        :max-date="maxDate"
        @confirm="onDateConfirm"
        @cancel="showDatePicker = false"
      />
    </van-popup>

    <!-- 时间选择器弹窗 -->
    <van-popup v-model="showTimePicker" position="bottom">
      <van-picker
        show-toolbar
        title="选择时间"
        :columns="timeColumns"
        @confirm="onTimeConfirm"
        @cancel="showTimePicker = false"
      />
    </van-popup>
  </div>
</template>

<script>
export default {
  name: 'PanelDateTime',
  data() {
    const now = new Date()
    return {
      selectedDate: now,
      selectedTime: now,
      showDatePicker: false,
      showTimePicker: false,
      minDate: new Date(2020, 0, 1),
      maxDate: new Date(2030, 11, 31),
      timeColumns: [
        {
          values: Array.from({ length: 24 }, (_, i) => String(i).padStart(2, '0')),
          defaultIndex: now.getHours()
        },
        {
          values: Array.from({ length: 60 }, (_, i) => String(i).padStart(2, '0')),
          defaultIndex: now.getMinutes()
        },
        {
          values: Array.from({ length: 60 }, (_, i) => String(i).padStart(2, '0')),
          defaultIndex: now.getSeconds()
        }
      ]
    }
  },
  computed: {
    formattedDate() {
      return this.$moment(this.selectedDate).format('YYYY-MM-DD')
    },
    formattedTime() {
      return this.$moment(this.selectedTime).format('HH:mm:ss')
    }
  },
  methods: {
    goBack() {
      this.$router.back()
    },
    handleDateClick() {
      this.showDatePicker = true
    },
    handleTimeClick() {
      this.showTimePicker = true
    },
    onDateConfirm(value) {
      this.selectedDate = value
      this.showDatePicker = false
      this.$toast(`日期已选择: ${this.formattedDate}`)
    },
    onTimeConfirm(value) {
      // value是一个数组，包含[小时, 分钟, 秒钟]的字符串值
      const hour = parseInt(value[0])
      const minute = parseInt(value[1])
      const second = parseInt(value[2])

      // 创建新的时间对象
      const newTime = new Date()
      newTime.setHours(hour, minute, second, 0)

      this.selectedTime = newTime
      this.showTimePicker = false
      this.$toast(`时间已选择: ${this.formattedTime}`)
    },
    handleSave() {
      this.$toast('日期时间已保存')
      // 这里可以调用API保存设置
    }
  }
}
</script>

<style lang="scss" scoped>
.panel-datetime {
  height: 100vh;
  background-color: #444;
  color: #fff;
  display: flex;
  flex-direction: column;

  .datetime-content {
    flex: 1;
    padding: 40px 20px 20px;
    display: flex;
    flex-direction: column;

    .calendar-icon-container {
      display: flex;
      justify-content: center;
      margin-bottom: 36px;

      .calendar-icon-bg {
        width: 100px;
        height: 100px;
        background-color: #666;
        border-radius: 50%;
        display: flex;
        align-items: center;
        justify-content: center;

        .calendar-icon {
          width: 40px;
          height: 40px;
        }
      }
    }

    .datetime-settings {
      .setting-item {
        margin-bottom: 40px;

        .setting-label {
          font-family: PingFangSC-Regular, sans-serif;
          font-weight: 400;
          font-size: 16px;
          line-height: 24px;
          color: #fff;
          margin-bottom: 12px;
        }

        .setting-value {
          font-family: PingFangSC-Regular, sans-serif;
          font-weight: 400;
          font-size: 16px;
          line-height: 24px;
          color: #ccc;
          cursor: pointer;
          padding: 8px 0;
          transition: color 0.2s;

          &:hover {
            color: #fff;
          }
        }

        .setting-divider {
          height: 1px;
          background-color: #666;
          margin-top: 12px;
        }
      }
    }
  }

  .save-button-container {
    padding: 20px;

    .save-button {
      width: 100%;
      height: 48px;
      background-color: #d32f2f;
      border: none;
      border-radius: 4px;
      font-family: PingFangSC-Medium, sans-serif;
      font-weight: 500;
      font-size: 16px;
      line-height: 24px;
      color: #fff;
      cursor: pointer;
      transition: background-color 0.2s;

      &:hover {
        background-color: #b71c1c;
      }

      &:active {
        background-color: #a00000;
        transform: translateY(1px);
      }
    }
  }
}
</style>
