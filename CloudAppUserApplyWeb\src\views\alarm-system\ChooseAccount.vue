<template>
  <div class="choose-account">
    <nav-bar title="Alarm System" @clickLeft="goBack" />
    <div class="choose-account-content">
      <div class="choose-account-choose">
        <div class="choose-title">Account</div>
        <van-radio-group v-model="selectedAccountType">
          <div class="radio-item" v-for="(item, index) in accountTypes" :key="index">
            <van-radio :name="item.value"
              >{{ item.label }}
              <template #icon="props">
                <theme-image
                  class="img-icon"
                  alt="check"
                  :imageName="props.checked ? 'alarm-system/radio_checked.png' : 'alarm-system/radio_no_check.png'"
                />
              </template>
            </van-radio>
          </div>
        </van-radio-group>
      </div>
    </div>

    <!-- 底部按钮 -->
    <div class="footer">
      <van-button class="footer-btn alarm-footer-btn" type="primary" @click="handleNext"> Next </van-button>
    </div>
  </div>
</template>

<script>
export default {
  name: 'ChooseAccount',
  data() {
    return {
      selectedAccountType: 'existing',
      accountTypes: [
        { label: 'I already have an account', value: 'existing' },
        { label: 'Use my Provision Cam2 account', value: 'provision' },
        { label: 'I want to open my own account', value: 'new' }
      ]
    }
  },
  methods: {
    goBack() {
      this.$router.back()
    },
    handleNext() {
      // 根据不同的账号类型进行不同的跳转
      const systemType = this.$route.query.systemType
      switch (this.selectedAccountType) {
        case 'existing':
          this.$router.push({
            path: '/alarmSystem/alarmLogin',
            query: {
              systemType
            }
          })
          break
        case 'provision':
          this.$router.push('/alarmSystem/provision-account')
          break
        case 'new':
          this.$router.push('/alarmSystem/register')
          break
        default:
          this.$toast('Invalid account type')
      }
    }
  }
}
</script>

<style lang="scss" scoped>
.choose-account {
  display: flex;
  flex-direction: column;
  height: 100%;
  background-color: #333;

  &-content {
    flex: 1;
    padding: 20px 16px;
    display: flex;
    flex-direction: column;
  }

  &-choose {
    margin-top: 20px;

    .choose-title {
      font-size: 18px;
      margin-bottom: 30px;
      text-align: center;
      font-weight: 400;
    }

    .radio-item {
      margin-bottom: 24px;

      .van-radio {
        display: flex;
        align-items: center;

        ::v-deep .van-radio__label {
          margin-left: 12px;
          font-size: 16px;
          line-height: 1.5;
        }

        ::v-deep .van-radio__icon {
          .theme-image-container {
            width: 20px;
            height: 20px;
          }
        }
      }
    }
  }
}
</style>
