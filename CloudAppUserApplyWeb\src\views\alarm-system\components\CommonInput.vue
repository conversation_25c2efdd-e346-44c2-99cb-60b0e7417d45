<template>
  <div class="common-input-wrapper">
    <div class="input-label" v-if="label">{{ label }}</div>

    <van-field
      ref="vanField"
      :type="inputType"
      :value="value"
      @input="$emit('input', $event)"
      :placeholder="placeholder"
      :class="['common-field', `border-${borderType}`]"
      @focus="handleFocus"
      @blur="handleBlur"
    >
      <!-- 右侧图标插槽 -->
      <template #right-icon>
        <!-- 清除图标 -->
        <theme-image
          v-if="showClearIcon"
          class="input-icon clear-icon"
          imageName="input_close.png"
          alt="clear input"
          @click="clearInput"
        />

        <!-- 密码显示/隐藏图标 -->
        <theme-image
          v-if="type === 'password'"
          class="input-icon password-toggle"
          :imageName="showPassword ? 'close_eye.png' : 'open_eye.png'"
          alt="toggle password visibility"
          @click="togglePassword"
        />
      </template>
    </van-field>

    <!-- 错误信息显示在组件最下方 -->
    <div class="error-msg" v-if="errorMessage">{{ errorMessage }}</div>
  </div>
</template>

<script>
export default {
  name: 'CommonInput',
  model: {
    prop: 'value',
    event: 'input'
  },
  props: {
    type: {
      type: String,
      default: 'text',
      validator: value => ['text', 'password', 'number', 'email', 'tel'].includes(value)
    },
    value: {
      type: [String, Number],
      default: ''
    },
    placeholder: {
      type: String,
      default: '',
      validator: value => typeof value === 'string'
    },
    label: {
      type: String,
      default: '',
      validator: value => typeof value === 'string'
    },
    errorMessage: {
      type: String,
      default: '',
      validator: value => typeof value === 'string'
    },
    borderType: {
      type: String,
      default: 'bottom', // 'bottom' | 'full'
      validator: value => ['bottom', 'full'].includes(value)
    }
  },
  data() {
    return {
      showPassword: false
    }
  },
  computed: {
    inputType() {
      if (this.type === 'password') {
        return this.showPassword ? 'text' : 'password'
      }
      return this.type
    },
    showClearIcon() {
      return this.value && this.value.length > 0
    }
  },
  methods: {
    handleFocus() {
      this.$emit('focus')
    },
    handleBlur() {
      this.$emit('blur')
    },
    clearInput() {
      this.$emit('input', '')
    },
    togglePassword() {
      this.showPassword = !this.showPassword
    },
    // 暴露 focus 方法给父组件使用
    focus() {
      this.$nextTick(() => {
        // 找到输入框元素
        const input = this.$refs.vanField?.$el?.querySelector('input')
        if (!input) return
        // 聚焦并将光标移到末尾
        input.focus()
        // 如果有值，将光标移动到末尾
        if (input.value.length > 0) {
          const length = input.value.length
          input.setSelectionRange(length, length)
        }
      })
    }
  }
}
</script>

<style lang="scss" scoped>
.common-input-wrapper {
  margin-bottom: 16px;
  .input-label {
    font-size: 14px;
    margin-bottom: 8px;
    font-weight: 500;
  }

  ::v-deep .van-cell {
    background: transparent;
    padding: 10px 0px;
  }

  // 底部边框样式（默认）
  ::v-deep .van-field.border-bottom {
    border-radius: 0;
  }

  // 完整边框样式
  ::v-deep .van-field.border-full {
    border-radius: 4px;
  }

  ::v-deep .common-field .van-field__right-icon {
    display: flex;
    flex-direction: row;
    align-items: center;
  }

  ::v-deep .common-field::after {
    border-bottom: none;
  }

  .input-icon {
    opacity: 0.6;
    transition: opacity 0.2s ease;

    &:hover {
      opacity: 1;
    }

    &.clear-icon {
      width: 20px;
      height: 20px;
    }

    &.password-toggle {
      width: 20px;
      height: 20px;
    }
  }

  // 自定义错误信息样式（参照 MobileInput）
  .error-msg {
    font-size: 12px;
    line-height: 14px;
    margin-top: 4px;
    padding-left: 0;
    text-align: left;
    display: block;
  }
}
</style>
