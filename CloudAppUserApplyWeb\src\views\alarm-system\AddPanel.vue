<template>
  <div class="add-panel">
    <nav-bar title="Add Panel" @clickLeft="goBack" />
    <div class="add-panel-content">
      <div class="form-section">
        <div class="form-title">Please specify your panel information</div>

        <div class="form-group">
          <van-field v-model="formData.panelName" placeholder="Panel name" class="custom-field" />
        </div>

        <div class="form-group">
          <van-field v-model="formData.panelSerial" placeholder="Panel serial" class="custom-field" />
        </div>

        <div class="form-group">
          <van-field v-model="formData.masterUserCode" placeholder="Master User code" class="custom-field" />
        </div>
      </div>

      <div class="form-footer">
        <van-button type="primary" block class="connect-button" @click="handleConnect" :disabled="!isFormValid">
          Connect
        </van-button>
      </div>
    </div>
  </div>
</template>

<script>
import { mapGetters } from 'vuex'

export default {
  name: 'AddPanel',
  data() {
    return {
      formData: {
        panelName: '',
        panelSerial: '',
        masterUserCode: ''
      }
    }
  },
  computed: {
    ...mapGetters('alarmSystem', ['systemType']),
    isFormValid() {
      return this.formData.panelName.trim() && this.formData.panelSerial.trim() && this.formData.masterUserCode.trim()
    }
  },
  methods: {
    goBack() {
      this.$router.back()
    },
    handleConnect() {
      if (!this.isFormValid) {
        this.$toast('Please fill in all fields')
        return
      }

      // 显示连接中的提示
      this.$toast.loading({
        message: 'Connecting to panel...',
        forbidClick: true,
        duration: 2000
      })

      // 模拟连接过程
      setTimeout(() => {
        this.$toast.clear()
        this.$toast.success('Panel connected successfully!')

        // 连接成功后返回面板列表
        setTimeout(() => {
          this.$router.back()
        }, 1000)
      }, 2000)
    }
  }
}
</script>

<style lang="scss" scoped>
.add-panel {
  display: flex;
  flex-direction: column;
  height: 100vh;
  background-color: #333;

  &-content {
    flex: 1;
    padding: 20px 16px;
    display: flex;
    flex-direction: column;
  }

  .form-section {
    flex: 1;
    padding-top: 20px;
  }

  .form-title {
    font-size: 16px;
    color: #fff;
    text-align: center;
    margin-bottom: 40px;
    line-height: 1.4;
  }

  .form-group {
    .form-label {
      font-size: 16px;
      color: #fff;
      margin-bottom: 12px;
      font-weight: 400;
    }

    .custom-field {
      background: transparent;
      border: none;
      border-bottom: 1px solid rgba(255, 255, 255, 0.3);
      border-radius: 0;
      padding: 0;

      ::v-deep .van-field__control {
        background: transparent;
        color: #fff;
        font-size: 16px;
        padding: 12px 0;
        border: none;

        &::placeholder {
          color: rgba(255, 255, 255, 0.5);
        }

        &:focus {
          border: none;
          outline: none;
        }
      }

      ::v-deep .van-field__body {
        border: none;
      }

      &:focus-within {
        border-bottom-color: rgba(255, 255, 255, 0.6);
      }
    }
  }

  .form-footer {
    padding: 20px 0 40px 0;

    .connect-button {
      background: #ff4444;
      border: none;
      border-radius: 8px;
      height: 48px;
      font-size: 16px;
      font-weight: 500;

      &:disabled {
        background: rgba(255, 68, 68, 0.5);
      }

      ::v-deep .van-button__text {
        color: #fff;
      }
    }
  }
}
</style>
