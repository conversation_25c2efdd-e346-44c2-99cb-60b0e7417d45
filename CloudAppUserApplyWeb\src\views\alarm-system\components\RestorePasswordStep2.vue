<template>
  <div class="restore-password-step">
    <div class="restore-password-content">
      <div class="restore-title">{{ $t('verifyYourEmail') }}</div>
      <div class="restore-subtitle">{{ $t('checkEmailAndInputCode') }}</div>

      <div class="restore-form">
        <common-input
          type="text"
          v-model="authCode"
          :placeholder="$t('authenticationCode')"
          :errorMessage="authCodeError"
          borderType="full"
        />
      </div>
    </div>

    <div class="restore-actions">
      <van-button class="action-btn ok-btn" type="cancel" @click="handleNext" :loading="loading">{{
        $t('ok')
      }}</van-button>
      <van-button class="action-btn cancel-btn" type="cancel" @click="handleCancel">{{ $t('cancel') }}</van-button>
    </div>
  </div>
</template>

<script>
import CommonInput from './CommonInput.vue'

export default {
  name: 'RestorePasswordStep2',
  components: {
    CommonInput
  },
  props: {
    email: {
      type: String,
      required: true
    }
  },
  data() {
    return {
      authCode: '',
      authCodeError: '',
      loading: false
    }
  },
  watch: {
    authCode(newVal) {
      // 当输入内容时，清除错误提示
      if (newVal && this.authCodeError === this.$t('pleaseEnterAuthCode')) {
        this.authCodeError = ''
      }
    }
  },
  methods: {
    async handleNext() {
      this.authCodeError = ''

      if (!this.authCode) {
        this.authCodeError = this.$t('pleaseEnterAuthCode')
        return
      }

      this.loading = true
      try {
        this.$emit('next', {
          email: this.email,
          authCode: this.authCode
        })
        this.clearForm()
      } catch (error) {
        console.error(error)
      } finally {
        this.loading = false
      }
    },
    handleCancel() {
      this.clearForm()
      this.$emit('cancel')
    },
    // 清除表单数据和错误信息
    clearForm() {
      this.authCode = ''
      this.authCodeError = ''
      this.loading = false
    }
  }
}
</script>
