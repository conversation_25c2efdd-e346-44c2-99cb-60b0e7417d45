<template>
  <div class="account-details">
    <nav-bar :title="$t('alarmSystem')" @clickLeft="goBack" />
    <div class="account-details-content">
      <div class="account-details-header">
        <div class="account-title">{{ $t('accountDetails') }}</div>
      </div>

      <div class="account-info">
        <div class="user-email">{{ userEmail }}</div>
        <div class="account-actions">
          <van-button type="primary" class="alarm-action-btn" @click="handleLogOut">
            {{ $t('logout') }}
          </van-button>
          <van-button type="primary" class="alarm-action-btn" @click="handlePanelList">
            {{ $t('panelList') }}
          </van-button>
        </div>
      </div>
    </div>

    <!-- 确认对话框 -->
    <van-dialog
      v-model="showLogoutDialog"
      :title="$t('confirmLogout')"
      :message="$t('confirmLogoutTips')"
      show-cancel-button
      @confirm="confirmLogout"
      @cancel="showLogoutDialog = false"
    />
  </div>
</template>

<script>
import { mapState } from 'vuex'

export default {
  name: 'AccountDetails',
  data() {
    return {
      showLogoutDialog: false
    }
  },
  computed: {
    ...mapState('alarmSystem', ['systemType', 'userInfo']),
    userEmail() {
      return this.userInfo?.email || '<EMAIL>'
    }
  },
  methods: {
    goBack() {
      this.$router.back()
    },
    handleLogOut() {
      this.showLogoutDialog = true
    },
    handlePanelList() {
      // 跳转到面板列表页面
      this.$router.push('/alarmSystem/panelList')
    },
    confirmLogout() {
      this.showLogoutDialog = false

      // 显示加载提示
      this.$toast.loading({
        message: this.$t('loggingOut'),
        forbidClick: true,
        duration: 1000
      })

      // 模拟登出操作
      setTimeout(() => {
        this.$toast.clear()

        // 清除用户登录状态
        this.$store.commit('alarmSystem/CLEAR_USER_INFO')

        // 跳转到登录页面
        this.$router.replace('/alarmSystem/chooseSystem')

        this.$toast.success(this.$t('loggedOutSuccess'))
      }, 1000)
    }
  }
}
</script>

<style lang="scss" scoped>
.account-details {
  display: flex;
  flex-direction: column;
  height: 100%;

  &-content {
    flex: 1;
    padding: 16px;
    display: flex;
    flex-direction: column;
  }

  &-header {
    margin-bottom: 8px;

    .account-title {
      font-size: 20px;
      line-height: 28px;
      font-weight: 500;
    }
  }

  .account-info {
    display: flex;
    justify-content: space-between;
    align-items: flex-start;
  }

  .user-email {
    font-size: 16px;
    font-weight: 600;
    line-height: 24px;
    flex: 1;
  }

  .account-actions {
    display: flex;
    flex-direction: column;
    gap: 20px;
    margin-left: 20px;

    .van-button {
      width: 106px;
      height: 32px;
      border-radius: 4px;
      font-size: 16px;
      font-weight: 400;
      border: none;
      min-width: 100px;
      padding: 0 16px;
    }
  }
}

// 自定义对话框样式
::v-deep .van-dialog {
  .van-dialog__header {
    color: #333;
    font-weight: 600;
  }

  .van-dialog__message {
    color: #666;
    text-align: center;
  }

  .van-dialog__footer {
    .van-button {
      border: none;

      &.van-button--default {
        color: #666;
      }

      &.van-button--primary {
        background-color: #1989fa;
      }
    }
  }
}
</style>
