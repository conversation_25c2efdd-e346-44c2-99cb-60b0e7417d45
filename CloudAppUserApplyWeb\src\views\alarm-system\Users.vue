<template>
  <div class="users-page">
    <!-- 顶部导航栏 -->
    <nav-bar title="User" @clickLeft="goBack">
      <template #right>
        <div class="add-user-btn" @click="handleAddUser">
          <theme-image class="add-icon" imageName="add_circle.png" alt="add" />
        </div>
      </template>
    </nav-bar>

    <!-- 用户列表 -->
    <div class="users-content">
      <div class="users-list">
        <div v-for="user in usersList" :key="user.id" class="user-item" @click="handleUserClick(user)">
          <div class="user-icon">
            <theme-image class="user-indicator" imageName="alarm-system/user.png" alt="user" />
          </div>
          <div class="user-details">
            <div class="user-name-row">
              <div class="user-name">{{ user.name }}</div>
              <div class="user-badges">
                <span v-for="badge in user.badges" :key="badge" class="user-badge">{{ badge }}</span>
              </div>
            </div>
            <div class="user-email" v-if="user.email">{{ user.email }}</div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
export default {
  name: 'Users',
  data() {
    return {
      usersList: [
        {
          id: 1,
          name: 'User 1',
          email: '<EMAIL>',
          badges: ['1', '2']
        },
        {
          id: 2,
          name: 'User 2',
          email: '',
          badges: ['1', '2']
        },
        {
          id: 3,
          name: 'User 3',
          email: '',
          badges: ['1', '2']
        },
        {
          id: 4,
          name: 'User 4',
          email: '',
          badges: ['1', '2']
        },
        {
          id: 5,
          name: 'User 5',
          email: '',
          badges: ['1', '2']
        }
      ]
    }
  },
  methods: {
    goBack() {
      this.$router.back()
    },
    handleAddUser() {
      this.$toast('添加用户功能')
    },
    handleUserClick(user) {
      this.$toast(`查看用户: ${user.name}`)
    }
  }
}
</script>

<style lang="scss" scoped>
.users-page {
  height: 100vh;
  background-color: #444;
  color: #fff;
  display: flex;
  flex-direction: column;

  .add-user-btn {
    width: 20px;
    height: 20px;

    .add-icon {
      width: 24px;
      height: 24px;
    }
  }

  .users-content {
    flex: 1;
    overflow-y: auto;

    .users-list {
      background-color: #333;
      .user-item {
        display: flex;
        align-items: center;
        width: 100%;
        height: 65px;
        padding: 0 16px;
        box-sizing: border-box;
        border-bottom: 1px solid #444;
        cursor: pointer;

        &:last-child {
          border-bottom: none;
        }

        .user-icon {
          margin-right: 16px;

          .user-indicator {
            width: 32px;
            height: 32px;
          }
        }

        .user-details {
          flex: 1;
          min-width: 0;

          .user-name-row {
            display: flex;
            align-items: center;
            margin-bottom: 2px;

            .user-name {
              height: 18px;
              font-family: PingFangSC-Regular, sans-serif;
              font-weight: 400;
              font-size: 12px;
              line-height: 18px;
              color: #fff;
              margin-right: 8px;
            }

            .user-badges {
              width: 32px;
              height: 16px;
              background: #ababab;
              border-radius: 11px;
              display: flex;
              color: #333;

              .user-badge {
                flex: 1;
                display: flex;
                align-items: center;
                justify-content: center;
                font-size: 10px;
                font-weight: 400;

                &:not(:last-child) {
                  border-right: 1px solid #333;
                }
              }
            }
          }

          .user-email {
            height: 18px;
            font-family: PingFangSC-Regular, sans-serif;
            font-weight: 400;
            font-size: 12px;
            color: #ffffff99;
            line-height: 18px;
            overflow: hidden;
            text-overflow: ellipsis;
            white-space: nowrap;
          }
        }
      }
    }
  }
}
</style>
