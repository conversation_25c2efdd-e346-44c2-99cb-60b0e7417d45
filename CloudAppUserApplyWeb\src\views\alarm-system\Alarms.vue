<template>
  <div class="alarms-page">
    <!-- 顶部导航 -->
    <nav-bar :title="$t('alarms')" @clickLeft="goBack" />

    <!-- 页面内容 -->
    <div class="alarms-content">
      <!-- Action 模块 - 仅在Pima和Tyco系统中显示 -->
      <div v-if="isTycoSystem || isPimaSystem" class="action-section alarm-box-wrapper">
        <div class="action-title">{{ $t('action') }}</div>
        <div class="action-buttons">
          <!-- Tyco系统显示两个按钮 -->
          <template v-if="isTycoSystem">
            <button class="siren-btn" @click="handleActivateSiren" :disabled="loading">
              {{ $t('activateSiren') }}
            </button>
            <button class="siren-btn" @click="handleMuteSiren" :disabled="loading">
              {{ $t('muteSiren') }}
            </button>
          </template>

          <!-- Pima系统只显示一个按钮 -->
          <template v-else-if="isPimaSystem">
            <button class="siren-btn" @click="handleMuteSiren" :disabled="loading">
              {{ $t('muteSiren') }}
            </button>
          </template>
        </div>
      </div>
      <div v-if="loading || groupedAlarms.length" class="alarms-groups">
        <!-- 按日期分组显示 -->
        <div v-for="group in groupedAlarms" :key="group.date" class="date-group">
          <div class="date-header">{{ group.dateLabel }}</div>
          <div class="alarms-list">
            <div
              v-for="alarm in group.alarms"
              :key="alarm.id"
              class="alarm-item alarm-box-wrapper"
              @click="handleAlarmClick(alarm)"
            >
              <div class="alarm-icon">
                <theme-image class="alarm-indicator" imageName="alarm-system/alarm.png" alt="alarm" />
              </div>
              <div class="alarm-content">
                <div class="alarm-location">{{ alarm.location }}</div>
                <div class="alarm-description">{{ alarm.description }}</div>
              </div>
              <div class="alarm-right">
                <div class="alarm-icon-btn">
                  <theme-image class="alarm-live" imageName="alarm-system/alarm_live.png" alt="alarm" />
                </div>
                <div class="alarm-icon-btn">
                  <theme-image class="alarm-playback" imageName="alarm-system/alarm_playback.png" alt="alarm" />
                </div>
                <div class="alarm-time">{{ alarm.time }}</div>
              </div>
            </div>
          </div>
        </div>
      </div>
      <div v-else class="no-alarms">
        <span class="no-alarms-text">{{ $t('noAlarmsFound') }}</span>
      </div>
    </div>
  </div>
</template>

<script>
import { mapState, mapGetters } from 'vuex'
import { getEventLog } from '@/api/alarmSystem'

export default {
  name: 'Alarms',

  data() {
    return {
      loading: false,
      alarms: [],
      // 模拟数据
      mockData: {
        totalCount: 5,
        controlPanelEventsList: [
          {
            lineNumber: 1,
            logTime: '2024-01-15T14:41:27.7142',
            eventText: 'DELAY Front Door',
            eventName: 'Burglary Alarm',
            eventDescriptorHint: 'string',
            sourceType: 0,
            sourceName: 'string',
            group: 0,
            groupName: 'alarm',
            sourceID: 0,
            reportStatus: 0,
            priority: 0,
            viUID: 'string',
            partAssociationCSV: 'string',
            eventId: 0,
            msd: { msdTag: 0, msdttl: '2024-01-15T14:41:27.7142' }
          },
          {
            lineNumber: 2,
            logTime: '2024-01-15T10:30:15.1234',
            eventText: 'FIRE Kitchen Smoke Detector',
            eventName: 'Fire Alarm',
            eventDescriptorHint: 'string',
            sourceType: 0,
            sourceName: 'string',
            group: 0,
            groupName: 'alarm',
            sourceID: 0,
            reportStatus: 0,
            priority: 0,
            viUID: 'string',
            partAssociationCSV: 'string',
            eventId: 1,
            msd: { msdTag: 0, msdttl: '2024-01-15T10:30:15.1234' }
          },
          {
            lineNumber: 3,
            logTime: '2024-01-14T22:15:45.5678',
            eventText: 'PANIC Living Room',
            eventName: 'Panic Alarm',
            eventDescriptorHint: 'string',
            sourceType: 0,
            sourceName: 'string',
            group: 0,
            groupName: 'alarm',
            sourceID: 0,
            reportStatus: 0,
            priority: 0,
            viUID: 'string',
            partAssociationCSV: 'string',
            eventId: 2,
            msd: { msdTag: 0, msdttl: '2024-01-14T22:15:45.5678' }
          },
          {
            lineNumber: 4,
            logTime: '2024-01-14T18:20:30.9876',
            eventText: 'DELAY Back Door',
            eventName: 'Burglary Alarm',
            eventDescriptorHint: 'string',
            sourceType: 0,
            sourceName: 'string',
            group: 0,
            groupName: 'alarm',
            sourceID: 0,
            reportStatus: 0,
            priority: 0,
            viUID: 'string',
            partAssociationCSV: 'string',
            eventId: 3,
            msd: { msdTag: 0, msdttl: '2024-01-14T18:20:30.9876' }
          },
          {
            lineNumber: 5,
            logTime: '2024-01-13T16:45:12.3456',
            eventText: 'WINDOW Bedroom Window',
            eventName: 'Window Alarm',
            eventDescriptorHint: 'string',
            sourceType: 0,
            sourceName: 'string',
            group: 0,
            groupName: 'alarm',
            sourceID: 0,
            reportStatus: 0,
            priority: 0,
            viUID: 'string',
            partAssociationCSV: 'string',
            eventId: 4,
            msd: { msdTag: 0, msdttl: '2024-01-13T16:45:12.3456' }
          },
          {
            lineNumber: 6,
            logTime: '2024-01-13T16:45:12.3456',
            eventText: 'WINDOW Bedroom Window',
            eventName: 'Window Alarm',
            eventDescriptorHint: 'string',
            sourceType: 0,
            sourceName: 'string',
            group: 0,
            groupName: 'alarm',
            sourceID: 0,
            reportStatus: 0,
            priority: 0,
            viUID: 'string',
            partAssociationCSV: 'string',
            eventId: 4,
            msd: { msdTag: 0, msdttl: '2024-01-13T16:45:12.3456' }
          },
          {
            lineNumber: 7,
            logTime: '2024-01-13T16:45:12.3456',
            eventText: 'WINDOW Bedroom Window',
            eventName: 'Window Alarm',
            eventDescriptorHint: 'string',
            sourceType: 0,
            sourceName: 'string',
            group: 0,
            groupName: 'alarm',
            sourceID: 0,
            reportStatus: 0,
            priority: 0,
            viUID: 'string',
            partAssociationCSV: 'string',
            eventId: 4,
            msd: { msdTag: 0, msdttl: '2024-01-13T16:45:12.3456' }
          },
          {
            lineNumber: 8,
            logTime: '2024-01-13T16:45:12.3456',
            eventText: 'WINDOW Bedroom Window',
            eventName: 'Window Alarm',
            eventDescriptorHint: 'string',
            sourceType: 0,
            sourceName: 'string',
            group: 0,
            groupName: 'alarm',
            sourceID: 0,
            reportStatus: 0,
            priority: 0,
            viUID: 'string',
            partAssociationCSV: 'string',
            eventId: 4,
            msd: { msdTag: 0, msdttl: '2024-01-13T16:45:12.3456' }
          },
          {
            lineNumber: 9,
            logTime: '2024-01-13T16:45:12.3456',
            eventText: 'WINDOW Bedroom Window',
            eventName: 'Window Alarm',
            eventDescriptorHint: 'string',
            sourceType: 0,
            sourceName: 'string',
            group: 0,
            groupName: 'alarm',
            sourceID: 0,
            reportStatus: 0,
            priority: 0,
            viUID: 'string',
            partAssociationCSV: 'string',
            eventId: 4,
            msd: { msdTag: 0, msdttl: '2024-01-13T16:45:12.3456' }
          },
          {
            lineNumber: 10,
            logTime: '2024-01-13T16:45:12.3456',
            eventText: 'WINDOW Bedroom Window',
            eventName: 'Window Alarm',
            eventDescriptorHint: 'string',
            sourceType: 0,
            sourceName: 'string',
            group: 0,
            groupName: 'alarm',
            sourceID: 0,
            reportStatus: 0,
            priority: 0,
            viUID: 'string',
            partAssociationCSV: 'string',
            eventId: 4,
            msd: { msdTag: 0, msdttl: '2024-01-13T16:45:12.3456' }
          },
          {
            lineNumber: 11,
            logTime: '2024-01-13T16:45:12.3456',
            eventText: 'WINDOW Bedroom Window',
            eventName: 'Window Alarm',
            eventDescriptorHint: 'string',
            sourceType: 0,
            sourceName: 'string',
            group: 0,
            groupName: 'alarm',
            sourceID: 0,
            reportStatus: 0,
            priority: 0,
            viUID: 'string',
            partAssociationCSV: 'string',
            eventId: 4,
            msd: { msdTag: 0, msdttl: '2024-01-13T16:45:12.3456' }
          }
        ]
      }
    }
  },

  computed: {
    ...mapState('alarmSystem', ['siteLoginInfo']),
    ...mapGetters('alarmSystem', ['systemType', 'isTycoSystem', 'isRiscoSystem', 'isPimaSystem', 'canFetchPanelState']),

    // 按日期分组的警报
    groupedAlarms() {
      if (!this.alarms.length) return []

      const groups = {}
      const today = this.$moment().format('YYYY-MM-DD')

      this.alarms.forEach(alarm => {
        const alarmDate = this.$moment(alarm.rawData.logTime).format('YYYY-MM-DD')

        if (!groups[alarmDate]) {
          groups[alarmDate] = {
            date: alarmDate,
            dateLabel: alarmDate === today ? 'Today' : alarmDate,
            alarms: []
          }
        }

        groups[alarmDate].alarms.push(alarm)
      })

      // 按日期排序，最新的在前
      return Object.values(groups).sort((a, b) => this.$moment(b.date).valueOf() - this.$moment(a.date).valueOf())
    }
  },

  mounted() {
    // 获取警报列表
    this.fetchAlarms()
  },

  methods: {
    // 返回上一页
    goBack() {
      this.$router.back()
    },

    // 获取警报列表
    async fetchAlarms() {
      // 使用模拟数据进行开发测试
      const useMockData = true // 设置为 false 使用真实API

      if (useMockData) {
        this.loading = true
        this.$loading.show()
        // 模拟API延迟
        await new Promise(resolve => setTimeout(resolve, 1000))

        try {
          const response = { data: this.mockData }

          if (response.data && response.data.controlPanelEventsList) {
            this.alarms = response.data.controlPanelEventsList
              .map((event, index) => ({
                id: event.lineNumber || index,
                number: `${index + 1}.`,
                location: this.extractLocation(event.eventName || event.eventText),
                description: this.extractDescription(event.eventName || event.eventText),
                date: this.formatEventDate(event.logTime),
                time: this.formatEventTime(event.logTime),
                rawData: event
              }))
              .filter(item => item.rawData.groupName === 'alarm')
          } else {
            this.alarms = []
          }
        } catch (error) {
          console.error('Mock data error:', error)
          this.alarms = []
        } finally {
          this.loading = false
          this.$loading.hide()
        }
        return
      }

      // 真实API调用
      if (!this.canFetchPanelState) {
        console.warn('Missing required data for fetching alarms')
        return
      }

      this.loading = true
      this.$loading.show()
      try {
        const response = await getEventLog(this.siteLoginInfo.siteId, {
          offset: 0,
          count: 100, // 获取更多条数以便分组
          sessionToken: this.siteLoginInfo.sessionId
        })

        if (response.data && response.data.controlPanelEventsList) {
          this.alarms = response.data.controlPanelEventsList
            .map((event, index) => ({
              id: event.lineNumber || index,
              number: `${index + 1}.`,
              location: this.extractLocation(event.eventName || event.eventText),
              description: this.extractDescription(event.eventName || event.eventText),
              date: this.formatEventDate(event.logTime),
              time: this.formatEventTime(event.logTime),
              rawData: event
            }))
            .filter(item => item.rawData.groupName === 'alarm')
        } else {
          this.alarms = []
        }
      } catch (error) {
        console.error('Fetch alarms failed:', error)
        this.$toast(this.$t('getAlarmsFailed'))
        this.alarms = []
      } finally {
        this.loading = false
        this.$loading.hide()
      }
    },

    // 提取位置信息
    extractLocation(eventText) {
      if (!eventText) return ''
      // 尝试提取位置信息，如 "Front Door"
      const match = eventText.match(/([A-Za-z\s]+Door|[A-Za-z\s]+Window|[A-Za-z\s]+Zone)/i)
      return match ? match[1].trim() : 'Unknown Location'
    },

    // 提取描述信息
    extractDescription(eventText) {
      if (!eventText) return 'Unknown Event'
      // 如果包含 "Burglary" 等关键词，返回对应描述
      if (eventText.toLowerCase().includes('burglary')) return 'Burglary Alarm'
      if (eventText.toLowerCase().includes('fire')) return 'Fire Alarm'
      if (eventText.toLowerCase().includes('panic')) return 'Panic Alarm'
      return eventText
    },

    // 格式化事件日期
    formatEventDate(logTime) {
      if (!logTime) return ''

      try {
        const date = new Date(logTime)
        return this.$moment(date).format('DD/MM/YYYY')
      } catch (error) {
        console.error('Format date error:', error)
        return ''
      }
    },

    // 格式化事件时间
    formatEventTime(logTime) {
      if (!logTime) return ''

      try {
        const date = new Date(logTime)
        return this.$moment(date).format('HH:mm:ss')
      } catch (error) {
        console.error('Format time error:', error)
        return logTime
      }
    },

    // 处理警报点击
    handleAlarmClick(alarm) {
      console.log('Alarm clicked:', alarm)
      this.$toast(`查看警报: ${alarm.description}`)
    },

    // 处理激活警报器 (仅Tyco系统)
    handleActivateSiren() {
      console.log('Activate siren clicked')
      this.$toast(this.$t('sirenActivateSuccess'))
      // 这里可以添加实际的激活警报器API调用
      // 例如：await activateSirenAPI(this.siteLoginInfo.siteId, this.siteLoginInfo.sessionId)
    },

    // 处理静音警报器
    handleMuteSiren() {
      console.log('Mute siren clicked')
      this.$toast(this.$t('sirenMuteSuccess'))
      // 这里可以添加实际的静音API调用
      // 例如：await muteSirenAPI(this.siteLoginInfo.siteId, this.siteLoginInfo.sessionId)
    }
  }
}
</script>

<style lang="scss" scoped>
.alarms-page {
  display: flex;
  flex-direction: column;
  height: 100vh;

  .page-header {
    padding: 12px 16px;
    display: flex;
    align-items: center;

    .header-left {
      display: flex;
      align-items: center;

      .back-icon {
        width: 24px;
        height: 24px;
        margin-right: 12px;
        cursor: pointer;

        &:hover {
          opacity: 0.7;
        }
      }

      .page-title {
        font-size: 18px;
        font-weight: 600;
      }
    }
  }

  .alarms-content {
    display: flex;
    flex-direction: column;
    flex: 1;
    height: 0;
    padding: 0;

    .action-section {
      padding: 20px 15px 14px 15px;

      .action-title {
        font-size: 14px;
        font-weight: 500;
        line-height: 22px;
        margin-left: 11px;
        margin-bottom: 5px;
      }

      .action-buttons {
        display: flex;
        justify-content: space-between;
        gap: 15px;
        .siren-btn {
          flex: 1;
          padding: 5px 24px;
          border-radius: 6px;
          font-size: 14px;
          font-weight: 400;
          cursor: pointer;
          min-width: 120px;
        }
      }
    }

    .no-alarms {
      display: flex;
      align-items: center;
      justify-content: center;
      padding: 40px 0;

      .no-alarms-text {
        font-size: 14px;
        color: #ccc;
      }
    }

    .alarms-groups {
      flex: 1;
      overflow-y: auto;
      .date-group {
        .date-header {
          font-size: 14px;
          font-weight: 400;
          text-align: center;
          line-height: 22px;
          padding: 8px 0;
        }

        .alarms-list {
          .alarm-item {
            display: flex;
            align-items: center;
            padding: 10px 16px;
            margin-bottom: 1px;
            font-size: 14px;
            font-weight: 400;
            line-height: 18px;

            .alarm-icon {
              margin-right: 5px;

              .alarm-indicator {
                width: 38px;
                height: 38px;
              }
            }

            .alarm-content {
              flex: 1;
              width: 0px;
              .alarm-location {
                margin-bottom: 2px;
                overflow: hidden;
                text-overflow: ellipsis;
                white-space: nowrap;
              }
              .alarm-description {
                overflow: hidden;
                text-overflow: ellipsis;
                white-space: nowrap;
              }
            }

            .alarm-right {
              display: flex;
              align-items: center;
              .alarm-icon-btn {
                margin-right: 12px;
              }
            }
          }
        }
      }
    }
  }
}
</style>
