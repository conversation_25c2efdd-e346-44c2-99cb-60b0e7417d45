<template>
  <div class="notifications-page">
    <!-- 顶部导航栏 -->
    <div class="page-header">
      <div class="header-left">
        <van-icon name="arrow-left" @click="goBack" />
        <span class="page-title">{{ $t('notifications') }}</span>
      </div>
    </div>

    <!-- 页面内容 -->
    <div class="notifications-content">
      <!-- 通知方式选择 -->
      <div class="notification-methods">
        <div class="method-tabs">
          <div
            v-for="method in notificationMethods"
            :key="method.key"
            :class="['method-tab', { active: activeMethod === method.key }]"
            @click="switchMethod(method.key)"
          >
            {{ method.label }}
          </div>
        </div>
      </div>

      <!-- 通知设置列表 -->
      <div class="notification-settings">
        <div
          v-for="setting in notificationSettings"
          :key="setting.key"
          class="alarm-box-wrapper alarm-bottom-box notification-item"
        >
          <div class="setting-info">
            <span class="setting-name">{{ setting.name }}</span>
          </div>
          <div class="setting-control">
            <van-switch v-model="setting.enabled" size="20px" @change="handleSettingChange(setting)" />
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import { getNotificationsFilter, updateNotificationsFilter } from '@/api/alarmSystem'

import { mapGetters } from 'vuex'

export default {
  name: 'Notifications',
  computed: {
    // 从URL参数获取报警系统类型，优先级：URL参数 > store > 默认值
    ...mapGetters('alarmSystem', ['systemType']),

    // 根据系统类型显示不同的通知方式
    notificationMethods() {
      const methods = [{ key: 'push', label: this.$t('msgPushSwitch') }]

      // 只有Risco系统支持Email通知，Pima系统只有Push
      if (this.systemType === 'Risco') {
        methods.push({ key: 'email', label: this.$t('email') })
      }

      return methods
    },
    // 根据系统类型返回对应的通知设置列表
    notificationSettings() {
      if (this.systemType === 'Pima') {
        return this.pimaNotificationSettings
      } else {
        return this.defaultNotificationSettings
      }
    }
  },

  data() {
    return {
      activeMethod: 'push', // 当前选中的通知方式
      // 默认通知设置（非Pima系统使用）
      defaultNotificationSettings: [
        {
          key: 'alarms',
          name: this.$t('alarms'),
          enabled: true
        },
        {
          key: 'restore_events',
          name: this.$t('restoreEvents'),
          enabled: true
        },
        {
          key: 'arm_disarm_events',
          name: this.$t('armDisarmEvents'),
          enabled: true
        },
        {
          key: 'notice_events',
          name: this.$t('noticeEvents'),
          enabled: true
        },
        {
          key: 'duress_events',
          name: this.$t('duressEvents'),
          enabled: false
        }
      ],
      // Pima系统的通知设置（基于API返回的完整类型）
      pimaNotificationSettings: [
        {
          id: 0,
          key: 'enabled_all',
          name: this.$t('enableAll'),
          enabled: true,
          apiName: 'NotificationsFilterType-EnabledAll',
          isSpecial: true // 特殊标记，用于UI区分
        },
        {
          id: 1,
          key: 'burglary',
          name: this.$t('burglary'),
          enabled: true,
          apiName: 'NotificationsFilterType-Burglary'
        },
        {
          id: 2,
          key: 'panic',
          name: this.$t('panic'),
          enabled: true,
          apiName: 'NotificationsFilterType-Panic'
        },
        {
          id: 3,
          key: 'fire',
          name: this.$t('fire'),
          enabled: true,
          apiName: 'NotificationsFilterType-Fire'
        },
        {
          id: 4,
          key: 'duress',
          name: this.$t('duress'),
          enabled: false,
          apiName: 'NotificationsFilterType-Duress'
        },
        {
          id: 5,
          key: 'medical',
          name: this.$t('medical'),
          enabled: true,
          apiName: 'NotificationsFilterType-Medical'
        },
        {
          id: 6,
          key: 'tamper',
          name: this.$t('tamper'),
          enabled: true,
          apiName: 'NotificationsFilterType-Tamper'
        },
        {
          id: 7,
          key: 'faults',
          name: this.$t('faults'),
          enabled: true,
          apiName: 'NotificationsFilterType-Faults'
        },
        {
          id: 8,
          key: 'arm_disarm',
          name: this.$t('armDisarm'),
          enabled: true,
          apiName: 'NotificationsFilterType-ArmDisarm'
        },
        {
          id: 20,
          key: 'alarm_restore',
          name: this.$t('alarmRestore'),
          enabled: false,
          apiName: 'NotificationsFilterType-AlarmRestore'
        },
        {
          id: 90,
          key: 'picture',
          name: this.$t('picture'),
          enabled: true,
          apiName: 'NotificationsFilterType-Picture'
        },
        {
          id: 100,
          key: 'invalid_report',
          name: this.$t('invalidReportCode'),
          enabled: false,
          apiName: 'NotificationsFilterType-InvalidReportCode'
        }
      ],
      // Pima系统的原始通知配置数据
      pimaNotificationFilters: []
    }
  },
  mounted() {
    // 如果当前系统不支持Email，确保activeMethod为push
    if (this.systemType === 'Pima' && this.activeMethod === 'email') {
      this.activeMethod = 'push'
    }
    // 如果是Pima系统，加载推送配置
    if (this.systemType === 'Pima') {
      this.loadPimaNotificationSettings()
    }
  },

  methods: {
    goBack() {
      this.$router.back()
    },

    switchMethod(method) {
      this.activeMethod = method
    },

    handleSettingChange(setting) {
      // 这里可以调用API保存设置
      this.saveNotificationSetting(setting)
    },

    // 加载Pima系统的通知设置
    async loadPimaNotificationSettings() {
      this.$loading.show()
      try {
        const reqData = {
          header: {
            webUserId: '...', // 从store或其他地方获取
            osType: '2', // Web浏览器
            osVersion: '4.4.1',
            appVersion: '2.8.0',
            pairEntityId: '...', // 从store获取
            sessionToken: '...' // 从store获取
          },
          data: null
        }

        const response = await getNotificationsFilter(reqData)

        if (response.data && Array.isArray(response.data)) {
          this.pimaNotificationFilters = response.data
          this.mapPimaFiltersToSettings(response.data)
        }
      } catch (error) {
        console.error('加载Pima通知配置失败:', error)
        this.$toast(this.$t('operationFail'))
        // 使用默认配置
        this.useMockPimaData()
      } finally {
        this.$loading.hide()
      }
    },

    // 将Pima API返回的数据映射到本地设置
    mapPimaFiltersToSettings(filters) {
      // 根据API返回的数据更新本地Pima设置
      filters.forEach(filter => {
        // 通过apiName匹配找到对应的本地设置项
        const localSetting = this.pimaNotificationSettings.find(setting => setting.apiName === filter.Name)

        if (localSetting) {
          // 更新本地设置的启用状态和ID
          localSetting.enabled = filter.Enabled
          localSetting.id = filter.ID
        }
      })
    },

    // 使用模拟数据（当API调用失败时）
    useMockPimaData() {
      this.pimaNotificationFilters = [
        {
          ID: 0,
          Name: 'NotificationsFilterType-EnabledAll',
          Enabled: true
        },
        {
          ID: 1,
          Name: 'NotificationsFilterType-Burglary',
          Enabled: true
        },
        {
          ID: 2,
          Name: 'NotificationsFilterType-Panic',
          Enabled: true
        },
        {
          ID: 3,
          Name: 'NotificationsFilterType-Fire',
          Enabled: true
        },
        {
          ID: 4,
          Name: 'NotificationsFilterType-Duress',
          Enabled: false
        },
        {
          ID: 5,
          Name: 'NotificationsFilterType-Medical',
          Enabled: true
        },
        {
          ID: 6,
          Name: 'NotificationsFilterType-Tamper',
          Enabled: true
        },
        {
          ID: 7,
          Name: 'NotificationsFilterType-Faults',
          Enabled: true
        },
        {
          ID: 8,
          Name: 'NotificationsFilterType-ArmDisarm',
          Enabled: true
        },
        {
          ID: 20,
          Name: 'NotificationsFilterType-AlarmRestore',
          Enabled: false
        },
        {
          ID: 90,
          Name: 'NotificationsFilterType-Picture',
          Enabled: true
        },
        {
          ID: 100,
          Name: 'NotificationsFilterType-InvalidReportCode',
          Enabled: false
        }
      ]
      this.mapPimaFiltersToSettings(this.pimaNotificationFilters)
    },

    async saveNotificationSetting(setting) {
      try {
        if (this.systemType === 'Pima') {
          // Pima系统使用专门的API
          await this.savePimaNotificationSetting(setting)
        } else {
          // 其他系统的保存逻辑
          console.log('保存通知设置:', {
            method: this.activeMethod,
            setting: setting.key,
            enabled: setting.enabled
          })
        }
      } catch (error) {
        console.error('保存通知设置失败:', error)
        this.$toast(this.$t('operationFailed'))
        // 恢复开关状态
        setting.enabled = !setting.enabled
      }
    },

    // 保存Pima系统的通知设置
    async savePimaNotificationSetting(setting) {
      // 更新本地的pimaNotificationFilters数据
      const updatedFilters = this.pimaNotificationFilters.map(filter => {
        // 通过apiName匹配找到对应的filter并更新
        if (filter.Name === setting.apiName) {
          return { ...filter, Enabled: setting.enabled }
        }
        return filter
      })

      // 根据新的接口要求，data应该是启用的通知类型ID数组
      const enabledNotificationIds = updatedFilters
        .filter(filter => filter.Enabled) // 只保留启用的通知类型
        .map(filter => filter.ID) // 提取ID

      const reqData = {
        header: {
          webUserId: '...', // 从store获取
          osType: '2',
          osVersion: '4.4.1',
          appVersion: '2.8.0',
          pairEntityId: '...', // 从store获取
          sessionToken: '...' // 从store获取
        },
        data: enabledNotificationIds // 传递启用的通知类型ID数组
      }

      const response = await updateNotificationsFilter(reqData)

      if (response.data !== undefined) {
        this.pimaNotificationFilters = updatedFilters
        this.$toast(this.$t('operationSuccess'))
      } else {
        throw new Error('保存失败')
      }
    }
  }
}
</script>

<style lang="scss" scoped>
.notifications-page {
  height: 100vh;
  display: flex;
  flex-direction: column;
  .page-header {
    padding: 12px 16px;
    display: flex;
    align-items: center;
    .header-left {
      display: flex;
      align-items: center;
      .van-icon {
        font-size: 20px;
        margin-right: 12px;
        cursor: pointer;
      }
      .page-title {
        font-size: 18px;
        font-weight: 600;
      }
    }
  }
  .notifications-content {
    flex: 1;
    overflow-y: auto;
    .notification-methods {
      padding: 16px;
      .method-tabs {
        display: flex;
        border-radius: 8px;
        padding: 4px;
        .method-tab {
          flex: 1;
          padding: 12px 16px;
          text-align: center;
          font-size: 14px;
          font-weight: 500;
          border-radius: 6px;
          cursor: pointer;
          transition: all 0.2s ease;
        }
      }
    }
    .notification-settings {
      padding: 0;
      .notification-item {
        display: flex;
        align-items: center;
        justify-content: space-between;
        padding: 16px 20px;
        &:last-child {
          border-bottom: none;
        }
        .setting-info {
          flex: 1;
          .setting-name {
            font-size: 16px;
            font-weight: 400;
            line-height: 1.4;
          }
        }
        .setting-control {
          margin-left: 16px;
        }
      }
    }
  }
}
</style>
