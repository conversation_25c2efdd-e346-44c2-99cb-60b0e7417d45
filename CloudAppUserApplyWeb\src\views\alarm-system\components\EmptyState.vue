<template>
  <div class="empty-state">
    <div class="empty-icon">
      <theme-image class="empty-img" :imageName="imageName" :alt="imageAlt" />
    </div>
    <div class="empty-text">{{ text || $t('noData') }}</div>
    <div v-if="showAction" class="empty-action">
      <slot name="action"></slot>
    </div>
  </div>
</template>

<script>
export default {
  name: 'EmptyState',
  props: {
    // 图标名称
    imageName: {
      type: String,
      default: 'no_data.png'
    },
    // 图标alt文本
    imageAlt: {
      type: String,
      default: 'no data'
    },
    // 显示文本
    text: {
      type: String,
      default: ''
    },
    // 是否显示操作区域
    showAction: {
      type: Boolean,
      default: false
    },
    // 自定义样式类名
    customClass: {
      type: String,
      default: ''
    }
  }
}
</script>

<style lang="scss" scoped>
.empty-state {
  flex: 1;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  text-align: center;
  margin-top: 50px;

  .empty-icon {
    margin-bottom: 20px;

    .empty-img {
      width: 48px;
      height: 48px;
    }
  }

  .empty-text {
    font-size: 18px;
    margin-bottom: 8px;
    font-weight: 500;
    color: #666;
  }

  .empty-action {
    margin-top: 16px;
  }
}
</style>
