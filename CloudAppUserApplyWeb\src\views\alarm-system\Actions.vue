<template>
  <div class="actions-page">
    <!-- 顶部导航 -->
    <nav-bar :title="$t('action')" @clickLeft="goBack" />
    <!-- 状态 -->
    <div class="status-section alarm-box-wrapper">
      <div class="status-header">
        <span class="status-title">{{ $t('status') }}</span>
        <div class="wifi-icon">
          <theme-image
            class="connection-icon"
            :imageName="displayConnection ? 'alarm-system/online.png' : 'alarm-system/offline.png'"
            alt="connection"
          />
        </div>
      </div>
    </div>
    <!-- 操作 -->
    <div class="actions-content">
      <!-- Action (All Partitions) 区域 -->
      <div class="all-partitions-section alarm-box-wrapper">
        <div class="section-title">{{ $t('action') }} ({{ $t('allPartitions') }})</div>
        <div class="action-buttons">
          <button
            v-for="action in globalActions"
            :key="action.type"
            :class="['action-button', action.type]"
            @click="handleGlobalAction(action)"
          >
            <theme-image class="action-icon" :imageName="getActionIconName(action.type)" :alt="action.text" />
            <span class="action-text">{{ action.text }}</span>
          </button>
        </div>
      </div>

      <!-- 分区列表 -->
      <div class="partitions-section alarm-box-wrapper">
        <div v-for="partition in partitions" :key="partition.id" class="partition-item">
          <!-- 第一行：分区名称 -->
          <div class="partition-title">{{ partition.name }}</div>

          <!-- 第二行：状态图标 + 操作按钮 -->
          <div class="partition-controls">
            <theme-image
              class="partition-status-icon"
              :imageName="getStatusIconName(parsePartitionStatus(partition))"
              :alt="parsePartitionStatus(partition)"
            />

            <div class="partition-actions">
              <button
                v-for="action in partitionActions"
                :key="action.type"
                :class="['action-button', action.type]"
                @click="handlePartitionAction(partition, action)"
              >
                <theme-image class="action-icon" :imageName="getActionIconName(action.type)" :alt="action.text" />
                <span class="action-text">{{ action.text }}</span>
              </button>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import { mapState, mapMutations, mapActions, mapGetters } from 'vuex'
import { getStatusIconName } from '@/utils/alarmSystem'
import { armPanel, armPartition } from '@/api/alarmSystem'

export default {
  name: 'Actions',

  data() {
    return {
      loading: false,
      globalActions: [
        {
          type: 'home',
          icon: 'home-o',
          text: 'Home',
          statusCode: 2
        },
        {
          type: 'away',
          icon: 'away',
          text: 'Away',
          statusCode: 1
        },
        {
          type: 'disarm',
          icon: 'unlock',
          text: 'Disarm',
          statusCode: 0
        }
      ],
      partitionActions: [
        {
          type: 'home',
          icon: 'home-o',
          text: 'Home',
          statusCode: 2
        },
        {
          type: 'away',
          icon: 'away',
          text: 'Away',
          statusCode: 1
        },
        {
          type: 'disarm',
          icon: 'unlock',
          text: 'Disarm',
          statusCode: 0
        }
      ]
    }
  },

  computed: {
    ...mapState('alarmSystem', ['panelState', 'siteLoginInfo']),

    partitions() {
      return this.panelState.partitions || []
    },

    // 显示的连接状态（参照AlarmSystemStatus.vue）
    displayConnection() {
      return this.panelState.isOnline
    },

    ...mapGetters('alarmSystem', ['siteId', 'sessionId']),

    // 判断是否有足够的数据进行API调用
    canPerformAction() {
      return this.siteId && this.sessionId && !this.loading
    }
  },

  mounted() {
    // 如果store中没有siteLoginInfo数据，设置一些模拟数据用于测试
    if (!this.siteLoginInfo.siteId || !this.siteLoginInfo.sessionId) {
      console.warn('No siteLoginInfo found in store, using mock data for testing')
    }

    // 初始化一些模拟的分区数据用于测试
    this.initializeMockPartitions()
  },

  methods: {
    ...mapMutations('alarmSystem', ['SET_PANEL_STATE_INFO']),
    ...mapActions('alarmSystem', ['fetchPanelState']),
    getStatusIconName,
    // 返回上一页
    goBack() {
      this.$router.back()
    },

    // 初始化模拟分区数据
    initializeMockPartitions() {
      // 如果store中没有分区数据，创建一些模拟数据
      if (!this.partitions.length) {
        const mockPartitions = [
          {
            id: 1,
            name: 'Partition 1',
            status: 'disarm',
            alarmState: 0
          },
          {
            id: 2,
            name: 'Partition 2',
            status: 'home',
            alarmState: 0
          },
          {
            id: 3,
            name: 'Partition 3',
            status: 'arm',
            alarmState: 0
          },
          {
            id: 4,
            name: 'Partition 3',
            status: 'arm',
            alarmState: 0
          },
          {
            id: 5,
            name: 'Partition 3',
            status: 'arm',
            alarmState: 0
          },
          {
            id: 6,
            name: 'Partition 3',
            status: 'arm',
            alarmState: 0
          },
          {
            id: 7,
            name: 'Partition 3',
            status: 'arm',
            alarmState: 0
          },
          {
            id: 8,
            name: 'Partition 3',
            status: 'arm',
            alarmState: 0
          }
        ]

        // 构造模拟的面板状态数据
        const mockPanelData = {
          partitions: mockPartitions,
          state: {
            status: [1],
            isOnline: false,
            media: 1
          }
        }

        // 更新store中的面板状态
        this.SET_PANEL_STATE_INFO(mockPanelData)
        console.log('Initialized mock partition data for testing')
      }
    },

    // 解析分区状态
    parsePartitionStatus(partition) {
      // 如果分区有直接的status字段
      if (partition.status) {
        return partition.status.toUpperCase()
      }

      // 如果分区有状态码
      if (partition.statusCode !== undefined) {
        if ([0, 8, 16].includes(partition.statusCode)) {
          return 'DISARM'
        } else if ([1, 4, 5, 9, 15].includes(partition.statusCode)) {
          return 'ARMED_AWAY'
        } else if ([2, 6, 10, 12, 14].includes(partition.statusCode)) {
          return 'ARMED_HOME'
        } else if ([11, 13].includes(partition.statusCode)) {
          return 'MIXED'
        }
      }

      // 默认返回撤防状态
      return 'DISARM'
    },

    // 获取操作按钮图标名称
    getActionIconName(actionType) {
      const iconMap = {
        home: 'alarm-system/home.png',
        away: 'alarm-system/away.png',
        disarm: 'alarm-system/disarm.png'
      }
      return iconMap[actionType] || 'alarm-system/disarm.png'
    },

    // 处理全局操作
    async handleGlobalAction(action) {
      if (!this.canPerformAction) {
        console.warn('Missing required data for global action')
        return
      }
      this.loading = true
      this.$loading.show()
      try {
        console.log('Global action:', action)

        // 1. 调用API设置系统状态
        await this.performGlobalAction(action)

        // 2. 尝试刷新状态，失败时使用本地更新
        await this.handleStateRefresh(() => {
          const updatedPartitions = this.partitions.map(partition => ({
            ...partition,
            status: action.type
          }))
          this.updatePanelState(updatedPartitions)
        })

        this.$toast(this.$t('operationSuccess'))
      } catch (error) {
        this.handleActionError(error, 'Global action')
      } finally {
        this.loading = false
        this.$loading.hide()
      }
    },

    // 处理分区操作
    async handlePartitionAction(partition, action) {
      if (!this.canPerformAction) {
        console.warn('Missing required data for partition action')
        return
      }

      this.loading = true
      this.$loading.show()
      try {
        console.log('Partition action:', partition, action)

        // 1. 执行分区操作
        await this.performPartitionAction(partition, action)

        // 2. 尝试刷新状态，失败时使用本地更新
        await this.handleStateRefresh(() => {
          const updatedPartitions = this.partitions.map(p =>
            p.id === partition.id ? { ...p, status: action.type } : p
          )
          this.updatePanelState(updatedPartitions)
        })

        this.$toast(this.$t('operationSuccess'))
      } catch (error) {
        this.handleActionError(error, 'Partition action')
      } finally {
        this.loading = false
        this.$loading.hide()
      }
    },

    // 获取分区当前布防状态
    getPartitionArmedState(partition) {
      // 根据分区当前状态映射到布防状态码
      const statusMap = {
        disarm: 0, // 撤防
        arm: 1, // 离家布防
        home: 2 // 在家布防
      }
      return statusMap[partition.status] || 0
    },

    // 更新面板状态
    updatePanelState(updatedPartitions) {
      const mockPanelData = {
        ...this.panelState.data,
        partitions: updatedPartitions,
        state: {
          status: [1],
          isOnline: true,
          media: 1
        }
      }

      this.SET_PANEL_STATE_INFO(mockPanelData)
    },

    // 执行全局操作API调用
    async performGlobalAction(action) {
      try {
        await armPanel(this.siteId, {
          newSystemStatus: action.statusCode,
          sessionToken: this.sessionId
        })
      } catch (error) {
        // 抛出特定的API错误
        const apiError = new Error(`Global action API failed: ${error.message}`)
        apiError.type = 'API_ERROR'
        apiError.originalError = error
        throw apiError
      }
    },

    // 执行分区操作API调用
    async performPartitionAction(partition, action) {
      try {
        const partitionData = {
          id: partition.id,
          armedState: this.getPartitionArmedState(partition),
          readyState: action.statusCode,
          alarmState: partition.alarmState || 0,
          exitDelayT0: 0,
          lastArmFailReasons: []
        }

        await armPartition(this.siteId, {
          partitions: [partitionData],
          sessionToken: this.sessionId
        })
      } catch (error) {
        // 抛出特定的API错误
        const apiError = new Error(`Partition action API failed: ${error.message}`)
        apiError.type = 'API_ERROR'
        apiError.originalError = error
        throw apiError
      }
    },

    // 统一处理状态刷新，失败时执行回调
    async handleStateRefresh(fallbackCallback) {
      try {
        await this.refreshPanelState()
      } catch (error) {
        console.warn('Failed to refresh panel state, using local update:', error)
        // 刷新失败时执行回调进行本地更新
        if (fallbackCallback) {
          fallbackCallback()
        }
        // 抛出刷新错误，但不影响主操作的成功
        const refreshError = new Error(`State refresh failed: ${error.message}`)
        refreshError.type = 'REFRESH_ERROR'
        refreshError.originalError = error
        throw refreshError
      }
    },

    // 统一错误处理
    handleActionError(error, actionType) {
      console.error(`${actionType} failed:`, error)

      // 根据错误类型显示不同的提示信息
      switch (error.type) {
        case 'API_ERROR':
          this.$toast(this.$t('operationFail'))
          break
        case 'REFRESH_ERROR':
          // 刷新错误不影响操作成功，只记录日志
          console.warn('State refresh failed but action succeeded')
          break
        default:
          this.$toast(this.$t('operationFail'))
      }
    },

    // 从服务器刷新面板状态
    async refreshPanelState() {
      // 使用mapActions引入的fetchPanelState方法
      await this.fetchPanelState({
        siteId: this.siteId,
        sessionToken: this.sessionId,
        force: true // 强制刷新，忽略缓存
      })
    }
  }
}
</script>

<style lang="scss" scoped>
.actions-page {
  display: flex;
  flex-direction: column;
  height: 100vh;
  .status-section {
    padding: 12px 16px;
    margin: 10px 10px 0px 10px;

    .status-header {
      display: flex;
      justify-content: space-between;
      align-items: center;

      .status-title {
        font-size: 14px;
        font-weight: 400;
        line-height: 22px;
      }

      .wifi-icon {
        .connection-icon {
          width: 20px;
          height: 20px;
        }
      }
    }
  }
  .actions-content {
    flex: 1;
    height: 0;
    padding: 0px 10px;
    overflow: auto;

    .all-partitions-section {
      margin-top: 6px;
      padding: 12px 10px;

      .section-title {
        margin: 0 6px;
        font-size: 14px;
        font-weight: 500;
        line-height: 22px;
        margin-bottom: 5px;
      }

      .action-buttons {
        display: flex;
        gap: 10px;

        .action-button {
          flex: 1;
          display: flex;
          align-items: center;
          padding: 4px 5px;
          border-radius: 8px;
          cursor: pointer;

          .action-icon {
            width: 26px;
            height: 26px;
            margin-right: 5px;
          }

          .action-text {
            font-size: 14px;
            font-weight: 400;
          }
        }
      }
    }

    .partitions-section {
      margin-top: 6px;
      padding: 12px 10px;

      .partition-item {
        margin-bottom: 24px;

        .partition-title {
          margin: 0 6px;
          font-size: 14px;
          font-weight: 500;
          line-height: 22px;
          margin-bottom: 5px;
        }

        .partition-controls {
          display: flex;
          align-items: center;
          gap: 10px;

          .partition-status-icon {
            width: 26px;
            height: 26px;
            flex-shrink: 0;
          }

          .partition-actions {
            display: flex;
            gap: 10px;
            flex: 1;

            .action-button {
              flex: 1;
              display: flex;
              align-items: center;
              padding: 4px 5px;
              border-radius: 8px;
              cursor: pointer;
              transition: all 0.2s ease;
              min-height: 36px;

              .action-icon {
                width: 14px;
                height: 14px;
                margin-right: 4px;
              }

              .action-text {
                font-size: 12px;
                font-weight: 500;
              }
            }
          }
        }
      }
    }
  }
}
</style>
