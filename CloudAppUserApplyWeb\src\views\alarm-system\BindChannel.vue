<template>
  <div class="bind-channel">
    <nav-bar :title="$t('channelBind')" :showBack="false" />
    <div class="bind-channel-content">
      <div class="bind-channel-header">
        <div class="device-select" @click="handleSelectDevice">
          <span class="device-name">{{ currentDevice.label }}</span>
          <theme-image class="icon-img" imageName="alarm-system/down_mini.png" alt="down_mini" />
        </div>
        <div class="close-icon" @click="handleClose">
          <theme-image class="icon-img" imageName="alarm-system/cancel.png" alt="cancel" />
        </div>
      </div>
      <div class="device-list" v-show="showDeviceList">
        <div
          class="device-item alarm-bottom-box"
          v-for="item in deviceList"
          :key="item.id"
          @click="handleSelectChannel(item)"
        >
          <span class="item-label">{{ item.label }}</span>
        </div>
      </div>
      <div class="device-list channel-list" v-show="showChannelList">
        <van-radio-group v-model="selectedChannel">
          <div
            class="device-item alarm-bottom-box"
            v-for="(item, index) in channelList"
            :key="index"
            @click="selectedChannel = item.value"
          >
            <span class="item-label">{{ item.label }}</span>
            <van-radio :name="item.value">
              <template #icon="props">
                <theme-image
                  class="img-icon"
                  alt="check"
                  :imageName="props.checked ? 'alarm-system/radio_checked.png' : 'alarm-system/radio_no_check.png'"
                />
              </template>
            </van-radio>
          </div>
        </van-radio-group>
      </div>
    </div>
    <!-- 底部按钮 -->
    <div class="bind-channel-footer footer" v-show="!showDeviceList">
      <van-button class="footer-btn" type="primary" @click="handleSave">{{ $t('save') }}</van-button>
    </div>
  </div>
</template>

<script>
export default {
  name: 'BindChannel',
  data() {
    return {
      showDeviceList: false,
      showChannelList: false,
      currentDevice: {},
      selectedChannel: '',
      deviceList: [],
      channelList: []
    }
  },
  mounted() {
    for (let i = 0; i < 30; i++) {
      this.deviceList[i] = {
        id: i,
        label: `DeviceName ${i}`,
        value: `${i}`
      }
      this.channelList[i] = {
        id: i,
        label: `ChannelName ${i}`,
        value: `${i}`,
        checked: false
      }
    }
    this.currentDevice = this.deviceList[0]
  },
  methods: {
    handleSelectDevice() {
      this.showDeviceList = !this.showDeviceList
      this.showChannelList = false
    },
    handleSelectChannel(item) {
      this.currentDevice = item
      this.showDeviceList = false
      this.showChannelList = !this.showChannelList
    },
    handleSave() {},
    handleClose() {
      this.$router.back()
    }
  }
}
</script>

<style lang="scss" scoped>
.bind-channel {
  display: flex;
  flex-direction: column;
  height: 100%;

  &-content {
    flex: 1;
    height: 0px;
  }

  &-header {
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 14px 16px;
    .device-select {
      display: flex;
      align-items: center;
      justify-content: space-between;
      padding: 0 4px;
      height: 34px;
      width: 258px;
      border-radius: 34px;
      .device-name {
        width: 0px;
        flex: 1;
        font-size: 14px;
        font-weight: 400;
        line-height: 22px;
        // 超出部分省略号
        overflow: hidden;
        text-overflow: ellipsis;
        white-space: nowrap;
      }
    }
  }

  .device-list {
    height: calc(100% - 62px);
    overflow-y: auto;
    .device-item {
      position: relative;
      display: flex;
      justify-content: space-between;
      align-items: center;
      padding: 12px 16px 12px 16px;

      .item-label {
        flex: 1;
        width: 0px;
        font-size: 14px;
        font-weight: 400;
        line-height: 24px;
        // 超出部分省略号
        overflow: hidden;
        text-overflow: ellipsis;
        white-space: nowrap;
      }

      ::v-deep .van-radio {
        .van-radio__icon {
          height: auto;
        }
      }
    }

    .device-item:last-child {
      border-bottom: none;
    }
  }
  .channel-list {
    height: calc(100% - 122px);
  }

  &-footer .footer-btn {
    width: 119px;
  }
}
</style>
