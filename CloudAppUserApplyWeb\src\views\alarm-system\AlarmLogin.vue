<template>
  <div class="alarm-login">
    <nav-bar :title="$t('alarmSystem')" @clickLeft="goBack" />
    <div class="alarm-login-content">
      <div class="login-title">
        {{ isPimaSystem ? $t('configureAccountSettings') : $t('login') }}
      </div>
      <div class="login-form">
        <!-- Tyco specific inputs -->
        <template v-if="isTycoSystem">
          <common-input
            type="text"
            :label="$t('serverAddress')"
            v-model="loginForm.server"
            :placeholder="$t('serverAddress')"
            :errorMessage="errors.server"
            borderType="full"
          />
          <common-input
            type="email"
            :label="$t('email')"
            v-model="loginForm.email"
            :placeholder="$t('email')"
            :errorMessage="errors.email"
            borderType="full"
          />
          <common-input
            type="password"
            :label="$t('password')"
            v-model="loginForm.password"
            :placeholder="$t('password')"
            :errorMessage="errors.password"
            borderType="full"
          />
        </template>
        <!-- Risco specific inputs -->
        <template v-else-if="isRiscoSystem">
          <common-input
            type="text"
            v-model="loginForm.username"
            :placeholder="$t('username')"
            :errorMessage="errors.username"
          />
          <common-input
            type="password"
            v-model="loginForm.password"
            :placeholder="$t('password')"
            :errorMessage="errors.password"
          />
        </template>
        <!-- Pima specific inputs -->
        <template v-else-if="isPimaSystem">
          <common-input
            type="text"
            v-model="loginForm.name"
            :placeholder="$t('username')"
            :errorMessage="errors.name"
          />
          <common-input
            type="email"
            v-model="loginForm.email"
            :placeholder="$t('email')"
            :errorMessage="errors.email"
          />
          <common-input type="tel" v-model="loginForm.phone" :placeholder="$t('mobile')" :errorMessage="errors.phone" />
        </template>
      </div>
      <!-- 底部操作区域 -->
      <div class="login-actions">
        <van-button type="primary" class="alarm-action-btn" block @click="handleLogin">
          {{ $t('login') }}
        </van-button>
        <div class="action-links">
          <span class="register" @click="handleRegister" v-if="isTycoSystem || isRiscoSystem">{{
            $t('register')
          }}</span>
          <span class="forgot-password" @click="handleForgotPassword">
            {{ $t('forgotPassword') }}
          </span>
        </div>
      </div>
    </div>
    <restore-password v-model="showRestorePassword" @success="handleRestoreSuccess" />
  </div>
</template>

<script>
import CommonInput from './components/CommonInput.vue'
import { mapActions, mapMutations, mapGetters } from 'vuex'
import { validateEmail } from '@/utils/validate'
import RestorePassword from './components/RestorePassword.vue'
import { riscoLogin } from '@/api/alarmSystem'
import { setCacheData } from '@/utils/appbridge'

export default {
  name: 'AlarmLogin',
  components: {
    CommonInput,
    RestorePassword
  },
  data() {
    return {
      loginForm: {
        server: '',
        email: '',
        password: '',
        username: '',
        name: '',
        phone: ''
      },
      errors: {
        server: '',
        email: '',
        password: '',
        username: '',
        name: '',
        phone: ''
      },
      showRestorePassword: false
    }
  },
  computed: {
    ...mapGetters('alarmSystem', ['isPimaSystem', 'isRiscoSystem', 'isTycoSystem'])
  },
  methods: {
    ...mapActions('alarmSystem', ['saveRiscoLoginInfo']),
    ...mapMutations('alarmSystem', ['SET_USER_INFO']),
    goBack() {
      this.$router.back()
    },
    validateForm() {
      this.clearErrors()
      switch (this.systemType) {
        case 'Tyco':
          return this.validateTycoForm()
        case 'Risco':
          return this.validateRiscoForm()
        case 'Pima':
          return this.validatePimaForm()
        default:
          return false
      }
    },
    // 清除所有错误信息
    clearErrors() {
      this.errors = {
        server: '',
        email: '',
        password: '',
        username: '',
        name: '',
        phone: ''
      }
    },
    // 验证Tyco系统表单
    validateTycoForm() {
      let isValid = true
      if (!this.loginForm.server.trim()) {
        this.errors.server = this.$t('serverAddressRequired')
        isValid = false
      }
      if (!this.loginForm.email.trim()) {
        this.errors.email = this.$t('enterMemberEmail')
        isValid = false
      } else if (!validateEmail(this.loginForm.email)) {
        this.errors.email = this.$t('emailNameError')
        isValid = false
      }
      if (!this.loginForm.password) {
        this.errors.password = this.$t('pleaseEnterPwd')
        isValid = false
      }
      return isValid
    },
    // 验证Risco系统表单
    validateRiscoForm() {
      let isValid = true
      if (!this.loginForm.username.trim()) {
        this.errors.username = this.$t('pleaseEnterUser')
        isValid = false
      }
      if (!this.loginForm.password) {
        this.errors.password = this.$t('pleaseEnterPwd')
        isValid = false
      }
      return isValid
    },
    // 验证Pima系统表单
    validatePimaForm() {
      let isValid = true
      if (!this.loginForm.name.trim()) {
        this.errors.name = this.$t('pleaseEnterUser')
        isValid = false
      }
      if (!this.loginForm.email.trim()) {
        this.errors.email = this.$t('enterMemberEmail')
        isValid = false
      } else if (!validateEmail(this.loginForm.email)) {
        this.errors.email = this.$t('emailNameError')
        isValid = false
      }
      if (!this.loginForm.phone.trim()) {
        this.errors.phone = this.$t('mobileNotEmpty')
        isValid = false
      }
      return isValid
    },
    async handleLogin() {
      // 验证表单
      if (!this.validateForm()) {
        return
      }
      this.$loading.show()
      try {
        let loginResult
        // 根据系统类型执行不同的登录逻辑
        if (this.isRiscoSystem) {
          loginResult = await this.performRiscoLogin()
        } else {
          loginResult = await this.performMockLogin()
        }
        // 统一处理登录成功
        this.handleLoginSuccess(loginResult)
        this.$loading.hide()
        this.$toast.success(this.$t('loginSuccessful'))
        this.$router.push('/alarmSystem/accountDetails')
      } catch (error) {
        this.$loading.hide()
        this.handleLoginError(error)
      }
    },
    // 执行Risco系统登录
    async performRiscoLogin() {
      const loginData = {
        username: this.loginForm.username,
        password: this.loginForm.password
      }
      const response = await riscoLogin(loginData)
      // 检查响应状态
      if (!response || response.status !== 100) {
        const error = new Error(this.$t('invalidCredentials'))
        error.type = 'INVALID_CREDENTIALS'
        throw error
      }
      return {
        type: 'risco',
        response,
        userInfo: {
          username: this.loginForm.username,
          systemType: this.systemType,
          ...response.response
        }
      }
    },
    // 执行模拟登录（Tyco/Pima系统）
    async performMockLogin() {
      // 开发环境下可以添加延迟模拟网络请求
      if (process.env.NODE_ENV === 'development') {
        await new Promise(resolve => setTimeout(resolve, 800))
      }
      return {
        type: 'mock',
        userInfo: {
          email: this.loginForm.email,
          username: this.loginForm.username,
          name: this.loginForm.name,
          phone: this.loginForm.phone,
          systemType: this.systemType
        }
      }
    },
    // 处理登录成功后的操作
    handleLoginSuccess(loginResult) {
      const { type, response, userInfo } = loginResult
      if (type === 'risco') {
        // 保存Risco登录信息
        this.saveRiscoLoginInfo(response)
        this.SET_USER_INFO(response.response)
        setCacheData({
          key: 'alarmUserInfo',
          value: JSON.stringify(userInfo)
        })
      } else {
        // 保存其他系统用户信息
        this.SET_USER_INFO(userInfo)
      }
    },
    // 统一错误处理
    handleLoginError(error) {
      // 错误日志记录（生产环境可以替换为日志系统）
      if (process.env.NODE_ENV === 'development') {
        console.error('Login error:', error)
      }
      // 根据错误类型显示不同的错误信息
      let errorMessage = this.$t('loginFailed')
      if (error.type === 'INVALID_CREDENTIALS') {
        errorMessage = error.message
      } else if (error.response?.status === 401) {
        errorMessage = this.$t('invalidCredentials')
      } else if (error.response?.status === 403) {
        errorMessage = this.$t('accountLocked')
      } else if (error.message?.includes('timeout')) {
        errorMessage = this.$t('netTimeOut')
      }
      this.$toast.fail(errorMessage)
    },
    handleForgotPassword() {
      this.showRestorePassword = true
    },
    handleRestoreSuccess() {
      // 密码重置成功后可以做一些操作，比如提示用户使用新密码登录
      this.$toast(this.$t('passwordResetSuccess'))
    },
    handleRegister() {
      // 处理注册逻辑
      this.$router.push('/alarm/register')
    }
  }
}
</script>

<style lang="scss" scoped>
.alarm-login {
  display: flex;
  flex-direction: column;
  height: 100%;

  &-content {
    flex: 1;
    padding: 20px 16px;
    display: flex;
    flex-direction: column;
  }

  .login-title {
    font-size: 24px;
    margin-bottom: 16px;
    text-align: center;
  }

  .login-actions {
    margin-top: auto;
    padding-bottom: 20px;

    .van-button {
      height: 44px;
      font-size: 16px;
      border: none;
    }

    .action-links {
      display: flex;
      justify-content: space-between;
      margin-top: 16px;
      font-size: 14px;

      .register,
      .forgot-password {
        cursor: pointer;
        opacity: 0.8;

        &:hover {
          opacity: 1;
        }
      }

      // 当只有忘记密码链接时（Pima系统），居中显示
      .forgot-password:only-child {
        margin: 0 auto;
      }
    }
  }
}
</style>
