<template>
  <div class="restore-password-step">
    <div class="restore-password-content">
      <div class="restore-title">{{ $t('restorePassword') }}</div>
      <div class="restore-subtitle">{{ $t('inputEmailForRegistering') }}</div>

      <div class="restore-form">
        <common-input
          type="email"
          v-model="email"
          :placeholder="$t('email')"
          :errorMessage="emailError"
          borderType="full"
        />
      </div>
    </div>

    <div class="restore-actions">
      <van-button class="action-btn ok-btn" type="cancel" @click="handleNext" :loading="loading">{{
        $t('ok')
      }}</van-button>
      <van-button class="action-btn cancel-btn" type="cancel" @click="handleCancel">{{ $t('cancel') }}</van-button>
    </div>
  </div>
</template>

<script>
import { validateEmail } from '@/utils/validate'
import CommonInput from './CommonInput.vue'

export default {
  name: 'RestorePasswordStep1',
  components: {
    CommonInput
  },
  props: {
    visible: {
      type: Boolean,
      default: false
    }
  },
  data() {
    return {
      email: '',
      emailError: '',
      loading: false
    }
  },
  watch: {
    email(newVal) {
      // 当输入内容时，清除错误提示
      if (newVal && this.emailError === this.$t('emailNameNotEmpty')) {
        this.emailError = ''
      }
    }
  },
  methods: {
    async handleNext() {
      this.emailError = ''

      if (!this.email) {
        this.emailError = this.$t('emailNameNotEmpty')
        return
      }

      // 验证邮箱格式
      if (!validateEmail(this.email)) {
        this.emailError = this.$t('emailNameError')
        return
      }

      this.loading = true
      try {
        this.$emit('next', { email: this.email })
        this.clearForm()
      } catch (error) {
        console.error(error)
      } finally {
        this.loading = false
      }
    },
    handleCancel() {
      this.clearForm()
      this.$emit('cancel')
    },
    // 清除表单数据和错误信息
    clearForm() {
      this.email = ''
      this.emailError = ''
      this.loading = false
    }
  }
}
</script>
