<template>
  <van-popup
    :value="value"
    @input="$emit('input', $event)"
    :close-on-click-overlay="false"
    class="restore-password-popup"
  >
    <restore-password-step1 v-if="currentStep === STEPS.EMAIL_INPUT" @next="goToStep2" @cancel="handleCancel" />

    <restore-password-step2
      v-if="currentStep === STEPS.CODE_VERIFICATION"
      :email="formData.email"
      @next="goToStep3"
      @cancel="handleCancel"
    />

    <restore-password-step3
      v-if="currentStep === STEPS.PASSWORD_RESET"
      :email="formData.email"
      :authCode="formData.authCode"
      @submit="handleSubmit"
      @cancel="handleCancel"
    />
  </van-popup>
</template>

<script>
import RestorePasswordStep1 from './RestorePasswordStep1.vue'
import RestorePasswordStep2 from './RestorePasswordStep2.vue'
import RestorePasswordStep3 from './RestorePasswordStep3.vue'

// 步骤常量定义
const STEPS = {
  EMAIL_INPUT: 1,
  CODE_VERIFICATION: 2,
  PASSWORD_RESET: 3
}

export default {
  name: 'RestorePassword',
  model: {
    prop: 'value',
    event: 'input'
  },
  components: {
    RestorePasswordStep1,
    RestorePasswordStep2,
    RestorePasswordStep3
  },
  props: {
    value: {
      type: Boolean,
      default: false
    }
  },
  data() {
    return {
      currentStep: STEPS.EMAIL_INPUT,
      formData: this.getInitialFormData()
    }
  },
  computed: {
    STEPS() {
      return STEPS
    }
  },
  beforeDestroy() {
    this.formData = this.getInitialFormData()
  },
  methods: {
    getInitialFormData() {
      return {
        email: '',
        authCode: '',
        newPassword: ''
      }
    },
    goToStep2(data) {
      this.formData.email = data.email

      // 这里可以添加发送验证码的逻辑
      this.$toast.loading({
        message: this.$t('sendingVerificationCode'),
        forbidClick: true,
        duration: 0
      })

      // 模拟发送验证码
      setTimeout(() => {
        this.$toast.clear()
        this.$toast.success(this.$t('verificationCodeSent'))
        this.currentStep = STEPS.CODE_VERIFICATION
      }, 1500)
    },
    goToStep3(data) {
      this.formData.authCode = data.authCode

      // 这里可以添加验证码验证的逻辑
      this.$toast.loading({
        message: this.$t('verifyingCode'),
        forbidClick: true,
        duration: 0
      })

      // 模拟验证码验证
      setTimeout(() => {
        this.$toast.clear()
        this.$toast.success(this.$t('codeVerified'))
        this.currentStep = STEPS.PASSWORD_RESET
      }, 1500)
    },
    handleSubmit(data) {
      this.formData.newPassword = data.newPassword

      // 这里可以添加重置密码的逻辑
      this.$toast.loading({
        message: this.$t('resettingPassword'),
        forbidClick: true,
        duration: 0
      })

      // 模拟重置密码
      setTimeout(() => {
        this.$toast.clear()
        this.$toast.success(this.$t('passwordResetSuccessfully'))
        this.handleCancel(true) // 成功后关闭弹窗
      }, 1500)
    },
    handleCancel(isSuccess = false) {
      this.$emit('input', false)
      this.currentStep = STEPS.EMAIL_INPUT
      this.formData = this.getInitialFormData()

      if (isSuccess) {
        this.$emit('success')
      } else {
        this.$emit('cancel')
      }
    }
  }
}
</script>

<style lang="scss">
.restore-password-popup {
  width: 80%;
  max-width: 350px;
  border-radius: 8px;
  overflow: hidden;
}
.restore-password-step {
  .restore-password-content {
    width: 100%;
    padding: 20px;
    box-sizing: border-box;
  }
  .restore-title {
    font-size: 20px;
    margin-bottom: 16px;
  }
  .restore-subtitle {
    font-size: 14px;
    margin-bottom: 20px;
  }
  .restore-actions {
    display: flex;
    justify-content: space-between;
    .action-btn {
      width: 50%;
      height: 40px;
      background-color: transparent;
      font-size: 16px;
    }
    .cancel-btn {
      background-color: transparent;
    }
  }
}
</style>
