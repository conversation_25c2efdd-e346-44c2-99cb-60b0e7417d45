<template>
  <div class="troubles-page">
    <!-- 顶部导航栏 -->
    <nav-bar title="Troubles" @clickLeft="goBack" />
    <!-- 故障列表 -->
    <div class="troubles-content">
      <!-- 故障列表 -->
      <div v-if="troubleList.length" class="troubles-list">
        <div v-for="trouble in troubleList" :key="trouble.id" class="alarm-bottom-box trouble-item">
          <div class="trouble-icon">
            <theme-image class="trouble-indicator" imageName="alarm-system/trouble.png" alt="trouble" />
          </div>
          <div class="trouble-details">
            <div class="trouble-header">
              <theme-image class="zone-icon" imageName="alarm-system/user.png" alt="zone" />
              <span class="zone-info">{{ trouble.zone || 'Zone 1' }}</span>
              <span v-if="trouble.time" class="trouble-time">{{ trouble.time }}</span>
            </div>
            <div class="alarm-sub-text trouble-description text-over-ellipsis">{{ trouble.type }}</div>
          </div>
        </div>
      </div>
      <!-- 空状态 -->
      <empty-state v-else />
    </div>
  </div>
</template>

<script>
import { mapGetters, mapActions, mapState } from 'vuex'
import EmptyState from './components/EmptyState.vue'

// 缓存过期时间常量
const CACHE_DURATION_MS = 5 * 60 * 1000

export default {
  name: 'Troubles',
  components: {
    EmptyState
  },
  data() {
    return {}
  },
  computed: {
    ...mapGetters('alarmSystem', ['troubleList', 'systemType', 'siteId', 'sessionId', 'canFetchPanelState']),
    ...mapState('alarmSystem', {
      troubleData: state => state.troubleData
    }),
    // 检查数据是否需要刷新（5分钟内的数据认为是新鲜的）
    shouldRefreshData() {
      if (!this.troubleData.lastUpdated) return true
      const now = Date.now()
      return now - this.troubleData.lastUpdated > CACHE_DURATION_MS
    }
  },
  async created() {
    await this.loadTroubles()
  },
  methods: {
    ...mapActions('alarmSystem', ['fetchTroubles']),
    goBack() {
      this.$router.back()
    },
    // 加载Troubles数据
    async loadTroubles() {
      if (!this.canFetchPanelState) {
        console.warn('Missing required data for fetching troubles')
        return
      }
      // 如果已有数据且较新，则不重新获取
      if (this.troubleList.length && !this.shouldRefreshData) {
        return
      }
      this.$loading.show()
      try {
        await this.fetchTroubles({
          siteId: this.siteId,
          sessionToken: this.sessionId,
          systemType: this.systemType,
          count: 100
        })
      } catch (error) {
        console.error('Fetch troubles failed:', error)
        this.handleTroublesError(error)
      } finally {
        this.$loading.hide()
      }
    },
    // 错误处理
    handleTroublesError(error) {
      const errorMessage = error.code ? this.$t(error.code) : error.message
      this.$toast.fail(errorMessage)
    }
  }
}
</script>

<style lang="scss" scoped>
.troubles-page {
  display: flex;
  flex-direction: column;
  .troubles-content {
    flex: 1;
    overflow-y: auto;
    .troubles-list {
      background-color: #333;
      .trouble-item {
        width: 100%;
        height: 65px;
        display: flex;
        align-items: center;
        padding: 16px;
        border-radius: 8px;
        box-sizing: border-box;
        &:last-child {
          margin-bottom: 0;
        }
        .trouble-icon {
          margin-right: 16px;
          .trouble-indicator {
            width: 32px;
            height: 32px;
          }
        }
        .trouble-details {
          flex: 1;
          overflow: hidden;
          .trouble-header {
            display: flex;
            align-items: center;
            justify-content: space-between;
            margin-bottom: 8px;
            .zone-icon {
              width: 20px;
              height: 20px;
              margin-right: 8px;
            }
            .zone-info {
              font-size: 14px;
              font-weight: 400;
              flex: 1;
            }
            .trouble-time {
              font-size: 12px;
              color: #999;
              margin-left: 8px;
            }
          }
          .trouble-description {
            font-size: 16px;
            font-weight: 400;
            line-height: 1.4;
          }
        }
      }
    }
  }
}
</style>
