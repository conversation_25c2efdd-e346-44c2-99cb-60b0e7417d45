<template>
  <div class="alarm-bottom-navigation">
    <div
      v-for="(item, index) in navigationItems"
      :key="index"
      :class="['nav-item', { active: activeIndex === index }]"
      @click="handleItemClick(item, index)"
    >
      <theme-image
        :imageName="'alarm-system/' + (activeIndex === index ? item.activeImage : item.image)"
        :alt="item.alt"
        :class="['nav-icon', { active: activeIndex === index }]"
      />
    </div>
  </div>
</template>

<script>
export default {
  name: 'AlarmBottomNavigation',
  props: {
    initActiveIndex: {
      type: Number,
      default: 0
    },
    navigationItems: {
      type: Array,
      default: () => [
        {
          image: 'main.png',
          activeImage: 'main_active.png',
          alt: 'main',
          action: 'main',
          path: '/alarmSystem/panelMain'
        },
        {
          image: 'devices.png',
          activeImage: 'devices_active.png',
          alt: 'devices',
          action: 'devices',
          path: '/alarmSystem/panelDevices'
        },
        {
          image: 'outputs.png',
          activeImage: 'outputs_active.png',
          alt: 'outputs',
          action: 'outputs',
          path: '/alarmSystem/panelOutputs'
        },
        {
          image: 'events.png',
          activeImage: 'events_active.png',
          alt: 'events',
          action: 'events',
          path: '/alarmSystem/panelEvents'
        },
        {
          image: 'settings.png',
          activeImage: 'settings_active.png',
          alt: 'settings',
          action: 'settings',
          path: '/alarmSystem/panelSettings'
        }
      ]
    }
  },
  data() {
    return {
      activeIndex: this.initActiveIndex
    }
  },
  methods: {
    handleItemClick(item, index) {
      this.activeIndex = index
      this.$emit('item-click', { item, index })

      // 如果有路径配置，直接进行路由跳转
      if (item.path && item.path !== this.$route.path) {
        this.$router.push(item.path)
      }
    }
  },
  watch: {
    initActiveIndex: {
      handler(newVal) {
        this.activeIndex = newVal
      },
      immediate: true
    }
  }
}
</script>

<style lang="scss" scoped>
.alarm-bottom-navigation {
  height: 60px;
  display: flex;
  align-items: center;
  justify-content: space-around;
  z-index: 1000;
  padding: 0 20px;
  box-sizing: border-box;

  .nav-item {
    display: flex;
    align-items: center;
    justify-content: center;
    width: 48px;
    height: 48px;
    border-radius: 50%;
    cursor: pointer;

    .nav-icon {
      ::v-deep .theme-image-container {
        width: 24px;
        height: 24px;

        .theme-image {
          width: 100%;
          height: 100%;
          object-fit: contain;
        }
      }

      &.active {
        ::v-deep .theme-image-container {
          transform: scale(1.1);
        }
      }
    }
  }
}
</style>
